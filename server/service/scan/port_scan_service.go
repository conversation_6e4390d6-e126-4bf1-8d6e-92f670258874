package scan

import (
	"encoding/json"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"server/core/manager/dbpool"
	scanner "server/core/scanner/portscanner"
	"server/global"
	"server/model/request/scan"
	scanModel "server/model/scan"

	"go.uber.org/zap"
	"gorm.io/gorm"
)

type PortScanService struct {
	scanners map[uint64]*scanner.PortScanner
	mu       sync.RWMutex
}

// NewPortScanService 创建端口扫描服务
func NewPortScanService() *PortScanService {
	return &PortScanService{
		scanners: make(map[uint64]*scanner.PortScanner),
	}
}

// CreateScanTask 创建扫描任务
func (s *PortScanService) CreateScanTask(req scan.CreateScanTaskRequest, userID uint) (*scanModel.PortScanTask, error) {
	// 验证请求参数
	if err := req.Validate(); err != nil {
		return nil, fmt.Errorf("参数验证失败: %v", err)
	}

	// 解析目标和端口
	allTargets, err := s.parseTargets(req.Targets)
	if err != nil {
		return nil, fmt.Errorf("解析目标失败: %v", err)
	}

	// 先解析端口，获取JSON数据
	ports, err := s.parsePorts(req.PortRange, req.Ports)
	if err != nil {
		return nil, fmt.Errorf("解析端口失败: %v", err)
	}

	// 获取目标和端口的JSON字符串
	targetsJSON, err := req.GetTargetsJSON()
	if err != nil {
		return nil, fmt.Errorf("序列化目标失败: %v", err)
	}

	portsJSON, err := req.GetPortsJSON()
	if err != nil {
		return nil, fmt.Errorf("序列化端口失败: %v", err)
	}

	// 创建任务记录
	task := &scanModel.PortScanTask{
		Name:        req.Name,
		Description: req.Description,
		Targets:     string(targetsJSON),
		PortRange:   req.PortRange, // 保存端口范围
		Ports:       string(portsJSON),
		ScanType:    req.ScanType,
		ScanOrder:   req.ScanOrder, // 保存扫描顺序
		BatchSize:   req.BatchSize, // 保存并发数
		Timeout:     req.Timeout,
		Retries:     req.Retries, // 保存重试次数
		Status:      scanModel.TaskStatusPending,
		Progress:    0,
		CreatedBy:   userID,
	}

	if err := global.DB.Create(&task).Error; err != nil {
		return nil, fmt.Errorf("创建任务失败: %v", err)
	}

	// 任务创建成功，保持pending状态，等待手动启动
	global.LOG.Info("端口扫描任务创建成功",
		zap.Uint("taskID", task.ID),
		zap.String("name", task.Name),
		zap.Int("totalTargets", len(allTargets)),
		zap.Int("totalPorts", len(ports)),
		zap.String("status", "pending"))

	return task, nil
}

// StartScan 启动扫描
func (s *PortScanService) StartScan(taskID uint64) error {
	// 获取任务信息
	var task scanModel.PortScanTask
	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_task", func(db *gorm.DB) error {
		return db.First(&task, taskID).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("任务不存在")
		}
		return fmt.Errorf("获取任务失败: %v", err)
	}

	// 检查任务状态
	if !task.CanStart() {
		return fmt.Errorf("任务状态不允许启动: %s", task.Status)
	}

	// 异步执行扫描任务
	go s.executeFullScanTask(&task)

	global.LOG.Info("启动端口扫描任务成功",
		zap.Uint64("taskID", taskID),
		zap.String("name", task.Name))

	return nil
}

// executeFullScanTask 执行完整的扫描任务（包括主机发现）- 使用FScan
func (s *PortScanService) executeFullScanTask(task *scanModel.PortScanTask) {
	// 更新任务状态为运行中
	task.Status = scanModel.TaskStatusRunning
	task.Progress = 0
	s.updateTask(task)

	// 解析原始目标和端口
	allTargets, ports, err := s.parseTaskTargetsAndPorts(task)
	if err != nil {
		global.LOG.Error("解析任务目标失败",
			zap.Uint("taskID", task.ID),
			zap.Error(err))
		task.UpdateStatus(scanModel.TaskStatusFailed, err.Error())
		s.updateTask(task)
		return
	}

	// 执行主机发现（如果启用）
	targets := s.performHostDiscoveryWithFScan(task, allTargets)

	// 更新任务统计信息
	task.TotalTargets = len(targets)
	task.TotalPorts = len(ports)
	task.Progress = 15.0 // 主机发现完成，进度15%
	s.updateTask(task)

	// 转换目标为字符串列表
	targetStrings := make([]string, len(targets))
	for i, ip := range targets {
		targetStrings[i] = ip.String()
	}

	// 创建FScan端口扫描器
	fscanScanner := scanner.NewFScanPortScanner(
		task.BatchSize,
		time.Duration(task.Timeout)*time.Millisecond,
	)

	fscanScanner.Start()
	defer fscanScanner.Stop()

	// 创建指纹识别器
	fingerprint := scanner.NewFScanFingerprint()

	taskID := uint64(task.ID)

	// 定义进度回调
	progressCallback := func(progress float64) {
		// 端口扫描阶段占15-100%的进度
		scanProgress := 15.0 + (progress * 0.85)
		task.Progress = scanProgress
		s.updateTask(task)

		global.LOG.Debug("FScan扫描进度",
			zap.Uint64("taskID", taskID),
			zap.Float64("progress", scanProgress))
	}

	// 定义结果回调
	resultCallback := func(result scanner.FScanResult) {
		// 解析IP地址
		ip := net.ParseIP(result.IP)
		if ip == nil {
			global.LOG.Warn("无效的IP地址", zap.String("ip", result.IP))
			return
		}

		// 转换FScan结果为我们的格式
		scanResult := scanner.ScanResult{
			Target: scanner.ScanTarget{
				IP:       ip,
				Port:     result.Port,
				Protocol: result.Protocol,
			},
			Status:   result.State,
			Service:  result.Service,
			Banner:   result.Banner,
			ScanTime: time.Now(),
		}

		// 如果端口开放，进行指纹识别
		if result.State == "open" {
			fpResult := fingerprint.IdentifyService(result.IP, result.Port, result.Banner)
			if fpResult.Service != "unknown" {
				scanResult.Service = fpResult.Service
				// 注意：ScanResult结构体可能没有Version和Product字段
				// 我们将这些信息添加到Banner中
				if fpResult.Version != "" {
					scanResult.Banner += " [Version: " + fpResult.Version + "]"
				}
				if fpResult.Product != "" {
					scanResult.Banner += " [Product: " + fpResult.Product + "]"
				}
			}
		}

		// 保存扫描结果
		s.saveScanResult(taskID, scanResult)
	}

	// 启动FScan扫描
	err = fscanScanner.ScanPorts(targetStrings, ports, progressCallback, resultCallback)
	if err != nil {
		global.LOG.Error("FScan扫描失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		task.UpdateStatus(scanModel.TaskStatusFailed, err.Error())
		s.updateTask(task)
		return
	}

	// 扫描完成
	task.Progress = 100.0
	task.UpdateStatus(scanModel.TaskStatusCompleted)
	s.updateTask(task)

	global.LOG.Info("FScan扫描任务完成",
		zap.Uint64("taskID", taskID),
		zap.Int("targets", len(targets)),
		zap.Int("ports", len(ports)))
}

// performHostDiscoveryWithFScan 使用FScan进行主机发现
func (s *PortScanService) performHostDiscoveryWithFScan(task *scanModel.PortScanTask, allTargets []net.IP) []net.IP {
	// 解析任务配置以获取主机发现选项
	var enableHostDiscovery bool
	var enablePing bool
	var enableTCPPing bool
	var noPing bool

	// 从任务描述或其他字段解析配置（这里简化处理）
	// 实际应该从任务创建时保存的配置中读取
	enableHostDiscovery = true // 默认启用
	enablePing = true
	enableTCPPing = true
	noPing = false

	if !enableHostDiscovery || len(allTargets) <= 1 {
		global.LOG.Info("跳过主机发现，直接使用所有目标", zap.Int("targets", len(allTargets)))
		return allTargets
	}

	global.LOG.Info("开始FScan主机发现", zap.Int("totalTargets", len(allTargets)))

	// 创建FScan主机发现器
	discovery := scanner.NewFScanHostDiscovery(scanner.DiscoveryOptions{
		EnablePing: enablePing,
		EnableTCP:  enableTCPPing,
		NoPing:     noPing,
		Timeout:    int(task.Timeout), // 使用任务配置的超时时间
		Workers:    task.BatchSize,    // 使用任务配置的并发数
	})

	discovery.Start()
	defer discovery.Stop()

	// 主机发现进度计数器
	var discoveredCount int64
	totalHosts := len(allTargets)

	aliveTargets, err := discovery.DiscoverHosts(allTargets, func(status scanner.HostStatus) {
		// 更新主机发现进度
		discoveredCount++

		// 主机发现阶段占5-15%的进度
		discoveryProgress := 5 + int(float64(discoveredCount)/float64(totalHosts)*10)
		if discoveryProgress > 15 {
			discoveryProgress = 15
		}

		// 更新任务进度
		task.Progress = float64(discoveryProgress)
		s.updateTask(task)

		global.LOG.Debug("FScan主机发现进度",
			zap.String("ip", status.IP.String()),
			zap.Bool("alive", status.IsAlive),
			zap.String("method", status.Method),
			zap.Int64("discovered", discoveredCount),
			zap.Int("total", totalHosts),
			zap.Int("progress", discoveryProgress))
	})

	if err != nil {
		global.LOG.Warn("FScan主机发现失败，使用所有目标", zap.Error(err))
		return allTargets
	}

	global.LOG.Info("FScan主机发现完成",
		zap.Int("totalTargets", len(allTargets)),
		zap.Int("aliveTargets", len(aliveTargets)),
		zap.Float64("aliveRatio", float64(len(aliveTargets))/float64(len(allTargets))*100))

	// 如果没有发现存活主机，可能是网络问题或配置问题
	if len(aliveTargets) == 0 {
		global.LOG.Warn("未发现任何存活主机，可能需要调整发现策略")
		if noPing {
			global.LOG.Info("NoPing模式，回退到扫描所有目标")
			return allTargets
		}
	}

	return aliveTargets
}

// parseTaskTargetsAndPorts 解析任务的目标和端口
func (s *PortScanService) parseTaskTargetsAndPorts(task *scanModel.PortScanTask) ([]net.IP, []int, error) {
	// 解析目标
	var targets []string
	if err := json.Unmarshal([]byte(task.Targets), &targets); err != nil {
		return nil, nil, fmt.Errorf("解析目标失败: %v", err)
	}

	// 解析IP地址
	allTargets, err := s.parseTargets(targets)
	if err != nil {
		return nil, nil, fmt.Errorf("解析目标IP失败: %v", err)
	}

	// 解析端口
	var ports []int
	if task.Ports != "" {
		if err := json.Unmarshal([]byte(task.Ports), &ports); err != nil {
			return nil, nil, fmt.Errorf("解析端口列表失败: %v", err)
		}
	} else {
		ports, err = s.parsePorts(task.PortRange, nil)
		if err != nil {
			return nil, nil, fmt.Errorf("解析端口范围失败: %v", err)
		}
	}

	return allTargets, ports, nil
}

// StopScan 停止扫描
func (s *PortScanService) StopScan(taskID uint64) error {
	// 获取扫描器
	s.mu.RLock()
	portScanner, exists := s.scanners[taskID]
	s.mu.RUnlock()

	if !exists {
		return fmt.Errorf("扫描任务不存在或未运行")
	}

	// 停止扫描器
	portScanner.Stop()

	// 清理扫描器
	s.mu.Lock()
	delete(s.scanners, taskID)
	s.mu.Unlock()

	// 更新任务状态
	var task scanModel.PortScanTask
	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_task", func(db *gorm.DB) error {
		return db.First(&task, taskID).Error
	})

	if err == nil {
		task.UpdateStatus(scanModel.TaskStatusCancelled)
		s.updateTask(&task)
	}

	global.LOG.Info("停止扫描任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// GetScanTask 获取扫描任务
func (s *PortScanService) GetScanTask(taskID uint64) (*scanModel.PortScanTask, error) {
	var task scanModel.PortScanTask
	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_task", func(db *gorm.DB) error {
		return db.First(&task, taskID).Error
	})

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("任务不存在")
		}
		return nil, fmt.Errorf("获取任务失败: %v", err)
	}

	return &task, nil
}

// GetScanTaskList 获取扫描任务列表
func (s *PortScanService) GetScanTaskList(req scan.GetScanTaskListRequest) ([]scanModel.PortScanTask, int64, error) {
	req.SetDefaults()

	var tasks []scanModel.PortScanTask
	var total int64

	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_task_list", func(db *gorm.DB) error {
		query := db.Model(&scanModel.PortScanTask{})

		// 添加过滤条件
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		}
		if req.ScanType != "" {
			query = query.Where("scan_type = ?", req.ScanType)
		}
		if req.Keyword != "" {
			query = query.Where("name LIKE ? OR description LIKE ?", "%"+req.Keyword+"%", "%"+req.Keyword+"%")
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取分页数据
		offset := (req.Page - 1) * req.PageSize
		return query.Order("created_at DESC").Offset(offset).Limit(req.PageSize).Find(&tasks).Error
	})

	if err != nil {
		return nil, 0, fmt.Errorf("获取任务列表失败: %v", err)
	}

	return tasks, total, nil
}

// parseTargets 解析目标地址
func (s *PortScanService) parseTargets(targets []string) ([]net.IP, error) {
	var ips []net.IP

	for _, target := range targets {
		target = strings.TrimSpace(target)
		if target == "" {
			continue
		}

		// 检查是否为单个IP地址
		if ip := net.ParseIP(target); ip != nil {
			ips = append(ips, ip)
			continue
		}

		// 检查是否为CIDR格式
		if _, ipNet, err := net.ParseCIDR(target); err == nil {
			cidrIPs, err := s.expandCIDR(ipNet)
			if err != nil {
				return nil, fmt.Errorf("展开CIDR %s 失败: %v", target, err)
			}
			ips = append(ips, cidrIPs...)
			continue
		}

		// 检查是否为IP范围格式 (如: ***********-255)
		if rangeIPs, err := s.parseIPRange(target); err == nil {
			ips = append(ips, rangeIPs...)
			continue
		}

		// 检查是否为通配符格式 (如: 192.168.1.*)
		if wildcardIPs, err := s.parseWildcard(target); err == nil {
			ips = append(ips, wildcardIPs...)
			continue
		}

		// 尝试解析域名
		resolvedIPs, err := net.LookupIP(target)
		if err != nil {
			return nil, fmt.Errorf("无法解析目标 %s: 不是有效的IP、CIDR、IP范围、通配符或域名", target)
		}
		ips = append(ips, resolvedIPs...)
	}

	return ips, nil
}

// parsePorts 解析端口
func (s *PortScanService) parsePorts(portRange string, ports []int) ([]int, error) {
	if len(ports) > 0 {
		return ports, nil
	}

	if portRange == "" {
		return nil, fmt.Errorf("端口范围和端口列表都为空")
	}

	// 解析端口范围
	parts := strings.Split(portRange, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("端口范围格式错误")
	}

	start, err := strconv.Atoi(strings.TrimSpace(parts[0]))
	if err != nil {
		return nil, fmt.Errorf("起始端口号无效: %v", err)
	}

	end, err := strconv.Atoi(strings.TrimSpace(parts[1]))
	if err != nil {
		return nil, fmt.Errorf("结束端口号无效: %v", err)
	}

	var result []int
	for port := start; port <= end; port++ {
		result = append(result, port)
	}

	return result, nil
}

// expandCIDR 展开CIDR为IP列表
func (s *PortScanService) expandCIDR(ipNet *net.IPNet) ([]net.IP, error) {
	var ips []net.IP

	// 限制CIDR大小，避免内存溢出
	ones, bits := ipNet.Mask.Size()
	if bits-ones > 16 { // 最多65536个IP
		return nil, fmt.Errorf("CIDR范围过大，最多支持/16网段")
	}

	for ip := ipNet.IP.Mask(ipNet.Mask); ipNet.Contains(ip); s.incrementIP(ip) {
		ips = append(ips, net.ParseIP(ip.String()))
	}

	return ips, nil
}

// incrementIP IP地址递增
func (s *PortScanService) incrementIP(ip net.IP) {
	for j := len(ip) - 1; j >= 0; j-- {
		ip[j]++
		if ip[j] > 0 {
			break
		}
	}
}

// buildScanTargets 构建扫描目标
func (s *PortScanService) buildScanTargets(task *scanModel.PortScanTask) ([]scanner.ScanTarget, error) {
	// 解析目标IP
	var targets []string
	if err := json.Unmarshal([]byte(task.Targets), &targets); err != nil {
		return nil, fmt.Errorf("解析目标失败: %v", err)
	}

	ips, err := s.parseTargets(targets)
	if err != nil {
		return nil, fmt.Errorf("解析IP失败: %v", err)
	}

	// 解析端口
	var ports []int
	if task.Ports != "" {
		if err := json.Unmarshal([]byte(task.Ports), &ports); err != nil {
			return nil, fmt.Errorf("解析端口列表失败: %v", err)
		}
	} else {
		ports, err = s.parsePorts(task.PortRange, nil)
		if err != nil {
			return nil, fmt.Errorf("解析端口范围失败: %v", err)
		}
	}

	// 生成扫描目标
	var scanTargets []scanner.ScanTarget

	protocols := []string{"tcp"}
	if task.ScanType == scanModel.ScanTypeUDP {
		protocols = []string{"udp"}
	} else if task.ScanType == scanModel.ScanTypeBoth {
		protocols = []string{"tcp", "udp"}
	}

	for _, protocol := range protocols {
		targets := scanner.GenerateTargets(ips, ports, protocol)
		scanTargets = append(scanTargets, targets...)
	}

	// 根据扫描顺序处理
	if task.ScanOrder == scanModel.ScanOrderRandom {
		scanner.ShuffleTargets(scanTargets)
	}

	return scanTargets, nil
}

// updateTask 更新任务
func (s *PortScanService) updateTask(task *scanModel.PortScanTask) error {
	return dbpool.ExecuteDBOperationAsyncAndWait("update_scan_task", func(db *gorm.DB) error {
		return db.Save(task).Error
	})
}

// updateScanProgress 更新扫描进度
func (s *PortScanService) updateScanProgress(taskID uint64, progress scanner.ScanProgress) {
	err := dbpool.ExecuteDBOperationAsyncAndWait("update_scan_progress", func(db *gorm.DB) error {
		updates := map[string]interface{}{
			"progress":       progress.Progress,
			"open_ports":     progress.OpenPorts,
			"closed_ports":   progress.ClosedPorts,
			"filtered_ports": progress.FilteredPorts,
			"updated_at":     time.Now(),
		}

		// 如果扫描完成，更新状态
		if progress.Progress >= 100 {
			updates["status"] = scanModel.TaskStatusCompleted
			now := time.Now()
			updates["completed_at"] = &now

			// 注意：未知指纹保存功能已由FScan系统处理
		}

		return db.Model(&scanModel.PortScanTask{}).Where("id = ?", taskID).Updates(updates).Error
	})

	if err != nil {
		global.LOG.Error("更新扫描进度失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
	}
}

// saveScanResult 保存扫描结果
func (s *PortScanService) saveScanResult(taskID uint64, result scanner.ScanResult) {
	scanResult := &scanModel.PortScanResult{
		TaskID:       taskID,
		Target:       result.Target.IP.String(),
		Port:         result.Target.Port,
		Protocol:     result.Target.Protocol,
		Status:       result.Status,
		Service:      result.Service,
		Banner:       result.Banner,
		ResponseTime: result.ResponseTime.Milliseconds(),
		ScanTime:     result.ScanTime,
		Retries:      result.Retries,
	}

	// 注意：服务指纹识别现在由FScan系统在executeFullScanTask中处理
	// 这里不再需要重复的指纹识别逻辑

	if result.Error != nil {
		scanResult.ErrorMsg = result.Error.Error()
	}

	err := dbpool.ExecuteDBOperationAsyncAndWait("save_scan_result", func(db *gorm.DB) error {
		return db.Create(scanResult).Error
	})

	if err != nil {
		global.LOG.Error("保存扫描结果失败",
			zap.Uint64("taskID", taskID),
			zap.String("target", result.Target.IP.String()),
			zap.Int("port", result.Target.Port),
			zap.Error(err))
	}
}

// GetScanResults 获取扫描结果
func (s *PortScanService) GetScanResults(taskID uint64, req scan.GetScanResultsRequest) (*scanModel.ScanResultsResponse, error) {
	req.SetDefaults()

	var results []scanModel.PortScanResult
	var total int64

	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_results", func(db *gorm.DB) error {
		query := db.Model(&scanModel.PortScanResult{}).Where("task_id = ?", taskID)

		// 添加过滤条件
		if req.Target != "" {
			query = query.Where("target = ?", req.Target)
		}
		if req.Status != "" {
			query = query.Where("status = ?", req.Status)
		}
		if req.Protocol != "" {
			query = query.Where("protocol = ?", req.Protocol)
		}
		if req.Port > 0 {
			query = query.Where("port = ?", req.Port)
		}

		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return err
		}

		// 获取分页数据
		offset := (req.Page - 1) * req.PageSize
		return query.Order("target, port").Offset(offset).Limit(req.PageSize).Find(&results).Error
	})

	if err != nil {
		return nil, fmt.Errorf("获取扫描结果失败: %v", err)
	}

	// 获取统计信息
	statistics, err := s.getScanStatistics(taskID)
	if err != nil {
		global.LOG.Error("获取扫描统计失败", zap.Error(err))
		statistics = &scanModel.PortScanStatistics{TaskID: taskID}
	}

	response := &scanModel.ScanResultsResponse{
		TaskID:     taskID,
		Results:    results,
		Statistics: *statistics,
		Page:       req.Page,
		PageSize:   req.PageSize,
		Total:      total,
		HasMore:    int64(req.Page*req.PageSize) < total,
	}

	return response, nil
}

// getScanStatistics 获取扫描统计信息
func (s *PortScanService) getScanStatistics(taskID uint64) (*scanModel.PortScanStatistics, error) {
	var stats scanModel.PortScanStatistics
	stats.TaskID = taskID

	err := dbpool.ExecuteDBOperationAsyncAndWait("get_scan_statistics", func(db *gorm.DB) error {
		// 获取各状态端口数量
		var counts []struct {
			Status string
			Count  int
		}

		if err := db.Model(&scanModel.PortScanResult{}).
			Select("status, count(*) as count").
			Where("task_id = ?", taskID).
			Group("status").
			Find(&counts).Error; err != nil {
			return err
		}

		// 统计各状态数量
		for _, count := range counts {
			switch count.Status {
			case scanModel.PortStatusOpen:
				stats.OpenPorts = count.Count
			case scanModel.PortStatusClosed:
				stats.ClosedPorts = count.Count
			case scanModel.PortStatusFiltered:
				stats.FilteredPorts = count.Count
			default:
				stats.ErrorPorts = count.Count
			}
		}

		stats.TotalPorts = stats.OpenPorts + stats.ClosedPorts + stats.FilteredPorts + stats.ErrorPorts

		// 获取任务信息计算进度
		var task scanModel.PortScanTask
		if err := db.First(&task, taskID).Error; err != nil {
			return err
		}

		if task.TotalTargets > 0 && task.TotalPorts > 0 {
			expectedTotal := task.TotalTargets * task.TotalPorts
			if task.ScanType == scanModel.ScanTypeBoth {
				expectedTotal *= 2 // TCP和UDP
			}
			if expectedTotal > 0 {
				stats.ScanProgress = float64(stats.TotalPorts) / float64(expectedTotal) * 100
			}
		}

		// 计算耗时
		if task.StartedAt != nil {
			endTime := time.Now()
			if task.CompletedAt != nil {
				endTime = *task.CompletedAt
			}
			stats.ElapsedTime = int64(endTime.Sub(*task.StartedAt).Seconds())
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &stats, nil
}

// DeleteScanTask 删除扫描任务
func (s *PortScanService) DeleteScanTask(taskID uint64) error {
	// 先停止扫描（如果正在运行）
	s.StopScan(taskID)

	// 删除任务和相关结果
	err := dbpool.ExecuteDBOperationAsyncAndWait("delete_scan_task", func(db *gorm.DB) error {
		// 删除扫描结果
		if err := db.Where("task_id = ?", taskID).Delete(&scanModel.PortScanResult{}).Error; err != nil {
			return err
		}

		// 删除任务
		return db.Delete(&scanModel.PortScanTask{}, taskID).Error
	})

	if err != nil {
		return fmt.Errorf("删除扫描任务失败: %v", err)
	}

	global.LOG.Info("删除扫描任务成功", zap.Uint64("taskID", taskID))
	return nil
}

// GetScanStatistics 获取扫描统计信息（公开方法）
func (s *PortScanService) GetScanStatistics(taskID uint64) (*scanModel.PortScanStatistics, error) {
	return s.getScanStatistics(taskID)
}

// 注意：saveUnknownFingerprints 方法已移除，未知指纹保存功能由FScan系统处理

// parseIPRange 解析IP范围格式 (如: ***********-255, ********-********00)
func (s *PortScanService) parseIPRange(target string) ([]net.IP, error) {
	// 检查是否包含范围符号
	if !strings.Contains(target, "-") {
		return nil, fmt.Errorf("不是IP范围格式")
	}

	parts := strings.Split(target, "-")
	if len(parts) != 2 {
		return nil, fmt.Errorf("IP范围格式错误")
	}

	startIP := strings.TrimSpace(parts[0])
	endPart := strings.TrimSpace(parts[1])

	// 解析起始IP
	start := net.ParseIP(startIP)
	if start == nil {
		return nil, fmt.Errorf("起始IP格式错误: %s", startIP)
	}

	// 检查结束部分是否为完整IP
	if endIP := net.ParseIP(endPart); endIP != nil {
		// 完整IP范围 (如: ***********-*************)
		return s.expandIPRange(start, endIP)
	}

	// 检查是否为最后一段的范围 (如: ***********-255)
	if endNum, err := strconv.Atoi(endPart); err == nil {
		if endNum < 0 || endNum > 255 {
			return nil, fmt.Errorf("IP段范围错误: %d", endNum)
		}

		// 构造结束IP
		startParts := strings.Split(startIP, ".")
		if len(startParts) != 4 {
			return nil, fmt.Errorf("起始IP格式错误")
		}

		endIP := fmt.Sprintf("%s.%s.%s.%d", startParts[0], startParts[1], startParts[2], endNum)
		end := net.ParseIP(endIP)
		if end == nil {
			return nil, fmt.Errorf("构造结束IP失败")
		}

		return s.expandIPRange(start, end)
	}

	return nil, fmt.Errorf("无法解析IP范围: %s", target)
}

// parseWildcard 解析通配符格式 (如: 192.168.1.*, 10.0.*.*)
func (s *PortScanService) parseWildcard(target string) ([]net.IP, error) {
	if !strings.Contains(target, "*") {
		return nil, fmt.Errorf("不是通配符格式")
	}

	parts := strings.Split(target, ".")
	if len(parts) != 4 {
		return nil, fmt.Errorf("通配符IP格式错误")
	}

	// 计算通配符的组合数
	wildcardCount := 0
	for _, part := range parts {
		if part == "*" {
			wildcardCount++
		}
	}

	// 限制通配符数量，避免生成过多IP
	if wildcardCount > 2 {
		return nil, fmt.Errorf("通配符过多，最多支持2个*")
	}

	// 生成所有可能的IP组合
	return s.generateWildcardIPs(parts, 0, []string{})
}

// expandIPRange 展开IP范围
func (s *PortScanService) expandIPRange(start, end net.IP) ([]net.IP, error) {
	var ips []net.IP

	// 转换为4字节表示
	start4 := start.To4()
	end4 := end.To4()
	if start4 == nil || end4 == nil {
		return nil, fmt.Errorf("仅支持IPv4地址")
	}

	// 计算IP数量，限制范围大小
	startInt := ipToInt(start4)
	endInt := ipToInt(end4)

	if endInt < startInt {
		return nil, fmt.Errorf("结束IP不能小于起始IP")
	}

	count := endInt - startInt + 1
	if count > 65536 {
		return nil, fmt.Errorf("IP范围过大，最多支持65536个IP")
	}

	// 生成IP列表
	for i := startInt; i <= endInt; i++ {
		ip := intToIP(i)
		ips = append(ips, ip)
	}

	return ips, nil
}

// generateWildcardIPs 生成通配符IP列表
func (s *PortScanService) generateWildcardIPs(parts []string, index int, current []string) ([]net.IP, error) {
	if index == len(parts) {
		// 构造IP地址
		ipStr := strings.Join(current, ".")
		if ip := net.ParseIP(ipStr); ip != nil {
			return []net.IP{ip}, nil
		}
		return nil, nil
	}

	var allIPs []net.IP

	if parts[index] == "*" {
		// 通配符，生成0-255的所有值
		for i := 0; i <= 255; i++ {
			newCurrent := append(current, strconv.Itoa(i))
			ips, err := s.generateWildcardIPs(parts, index+1, newCurrent)
			if err != nil {
				return nil, err
			}
			allIPs = append(allIPs, ips...)

			// 限制生成的IP数量
			if len(allIPs) > 65536 {
				return nil, fmt.Errorf("通配符生成的IP过多，请缩小范围")
			}
		}
	} else {
		// 固定值
		newCurrent := append(current, parts[index])
		ips, err := s.generateWildcardIPs(parts, index+1, newCurrent)
		if err != nil {
			return nil, err
		}
		allIPs = append(allIPs, ips...)
	}

	return allIPs, nil
}

// ipToInt 将IP转换为整数
func ipToInt(ip net.IP) uint32 {
	ip = ip.To4()
	return uint32(ip[0])<<24 + uint32(ip[1])<<16 + uint32(ip[2])<<8 + uint32(ip[3])
}

// intToIP 将整数转换为IP
func intToIP(n uint32) net.IP {
	return net.IPv4(byte(n>>24), byte(n>>16), byte(n>>8), byte(n))
}
