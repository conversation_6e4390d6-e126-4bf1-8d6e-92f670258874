package portscanner

import (
	"fmt"
	"net"
	"server/global"
	"syscall"
	"time"

	"go.uber.org/zap"
)

// WindowScan TCP Window扫描
func (s *AdvancedScanner) WindowScan(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   "filtered",
	}

	if !s.hasRawSocketPermission() {
		result.Status = "error"
		result.Error = fmt.Errorf("Window扫描需要root权限")
		global.LOG.Warn("Window扫描权限不足，回退到TCP连接扫描")
		return s.fallbackTCPScan(target)
	}

	start := time.Now()

	// 执行真正的Window扫描
	status, err := s.performWindowScan(target.IP, target.Port)
	result.ResponseTime = time.Since(start)

	if err != nil {
		global.LOG.Debug("Window扫描失败，回退到TCP连接扫描",
			zap.String("target", target.IP.String()),
			zap.Int("port", target.Port),
			zap.Error(err))
		return s.fallbackTCPScan(target)
	}

	result.Status = status
	return result
}

// performWindowScan 执行真正的Window扫描
func (s *AdvancedScanner) performWindowScan(ip net.IP, port int) (string, error) {
	// Window扫描是ACK扫描的变种，通过检查RST响应中的TCP窗口字段来判断端口状态
	// 某些系统对开放和关闭的端口返回不同的窗口大小

	// 创建原始套接字
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return "", fmt.Errorf("创建原始套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 设置IP_HDRINCL选项
	err = syscall.SetsockoptInt(fd, syscall.IPPROTO_IP, syscall.IP_HDRINCL, 1)
	if err != nil {
		return "", fmt.Errorf("设置IP_HDRINCL失败: %v", err)
	}

	// 构造ACK包（Window扫描基于ACK扫描）
	packet, err := s.buildACKPacket(ip, port)
	if err != nil {
		return "", fmt.Errorf("构造ACK包失败: %v", err)
	}

	// 发送ACK包
	addr := &syscall.SockaddrInet4{Port: port}
	copy(addr.Addr[:], ip.To4())

	err = syscall.Sendto(fd, packet, 0, addr)
	if err != nil {
		return "", fmt.Errorf("发送ACK包失败: %v", err)
	}

	// 等待响应
	time.Sleep(100 * time.Millisecond)

	// Window扫描逻辑：
	// - 分析RST响应中的窗口字段
	// - 不同的窗口值可能表示不同的端口状态
	return s.analyzeWindowResponse(ip, port)
}

// analyzeWindowResponse 分析Window扫描响应
func (s *AdvancedScanner) analyzeWindowResponse(ip net.IP, port int) (string, error) {
	// Window扫描需要分析RST响应中的窗口字段
	// 这里实现真正的窗口分析
	status, windowSize, err := s.listenForWindowResponse(ip, port, 500*time.Millisecond)
	if err != nil {
		// 如果监听失败，回退到快速连接测试
		global.LOG.Debug("Window响应监听失败，回退到连接测试", zap.Error(err))
		quickStatus, _ := s.quickConnectTest(ip, port)
		return quickStatus, nil
	}

	// Window扫描逻辑：
	// - 如果收到RST且窗口大小 > 0，通常表示端口关闭
	// - 如果收到RST且窗口大小 = 0，可能表示端口开放
	// - 无响应可能表示端口被过滤
	if status == "rst_received" {
		if windowSize > 0 {
			return "closed", nil
		} else {
			// 窗口大小为0，可能是开放端口的特征
			quickStatus, _ := s.quickConnectTest(ip, port)
			if quickStatus == "open" {
				return "open", nil
			}
			return "closed", nil
		}
	}

	return "filtered", nil
}

// listenForWindowResponse 监听Window扫描响应并分析窗口大小
func (s *AdvancedScanner) listenForWindowResponse(ip net.IP, port int, timeout time.Duration) (string, uint16, error) {
	// 创建原始套接字用于监听响应
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return "", 0, fmt.Errorf("创建监听套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 设置接收超时
	tv := syscall.Timeval{
		Sec:  int64(timeout.Seconds()),
		Usec: int32(timeout.Nanoseconds() % 1e9 / 1e3),
	}
	err = syscall.SetsockoptTimeval(fd, syscall.SOL_SOCKET, syscall.SO_RCVTIMEO, &tv)
	if err != nil {
		return "", 0, fmt.Errorf("设置接收超时失败: %v", err)
	}

	// 监听响应
	buffer := make([]byte, 1500)
	startTime := time.Now()

	for time.Since(startTime) < timeout {
		n, _, err := syscall.Recvfrom(fd, buffer, 0)
		if err != nil {
			if errno, ok := err.(syscall.Errno); ok {
				if errno == syscall.EAGAIN || errno == syscall.EWOULDBLOCK {
					continue
				}
			}
			return "", 0, fmt.Errorf("接收数据失败: %v", err)
		}

		if n < 40 {
			continue
		}

		// 解析IP头
		if buffer[0]>>4 != 4 {
			continue
		}

		ipHeaderLen := int(buffer[0]&0x0F) * 4
		if n < ipHeaderLen+20 {
			continue
		}

		// 检查源IP
		srcIP := net.IPv4(buffer[12], buffer[13], buffer[14], buffer[15])
		if !srcIP.Equal(ip) {
			continue
		}

		// 解析TCP头
		tcpHeader := buffer[ipHeaderLen:]
		srcPort := int(tcpHeader[0])<<8 | int(tcpHeader[1])

		if srcPort != port {
			continue
		}

		// 检查TCP标志
		flags := tcpHeader[13]

		// 如果收到RST，提取窗口大小
		if flags&0x04 == 0x04 { // RST
			windowSize := uint16(tcpHeader[14])<<8 | uint16(tcpHeader[15])
			return "rst_received", windowSize, nil
		}
	}

	// 超时，无响应
	return "no_response", 0, nil
}
