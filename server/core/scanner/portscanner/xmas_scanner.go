package portscanner

import (
	"fmt"
	"math/rand"
	"net"
	"server/global"
	"syscall"
	"time"

	"go.uber.org/zap"
)

// XmasScan TCP Xmas扫描
func (s *AdvancedScanner) XmasScan(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   "filtered",
	}

	if !s.hasRawSocketPermission() {
		result.Status = "error"
		result.Error = fmt.Errorf("Xmas扫描需要root权限")
		global.LOG.Warn("Xmas扫描权限不足，回退到TCP连接扫描")
		return s.fallbackTCPScan(target)
	}

	start := time.Now()

	// 执行真正的Xmas扫描
	status, err := s.performXmasScan(target.IP, target.Port)
	result.ResponseTime = time.Since(start)

	if err != nil {
		global.LOG.Debug("Xmas扫描失败，回退到TCP连接扫描",
			zap.String("target", target.IP.String()),
			zap.Int("port", target.Port),
			zap.Error(err))
		return s.fallbackTCPScan(target)
	}

	result.Status = status
	return result
}

// performXmasScan 执行真正的Xmas扫描
func (s *AdvancedScanner) performXmasScan(ip net.IP, port int) (string, error) {
	// 创建原始套接字
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return "", fmt.Errorf("创建原始套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 设置IP_HDRINCL选项
	err = syscall.SetsockoptInt(fd, syscall.IPPROTO_IP, syscall.IP_HDRINCL, 1)
	if err != nil {
		return "", fmt.Errorf("设置IP_HDRINCL失败: %v", err)
	}

	// 构造Xmas包
	packet, err := s.buildXmasPacket(ip, port)
	if err != nil {
		return "", fmt.Errorf("构造Xmas包失败: %v", err)
	}

	// 发送Xmas包
	addr := &syscall.SockaddrInet4{Port: port}
	copy(addr.Addr[:], ip.To4())

	err = syscall.Sendto(fd, packet, 0, addr)
	if err != nil {
		return "", fmt.Errorf("发送Xmas包失败: %v", err)
	}

	// 等待响应
	time.Sleep(100 * time.Millisecond)

	// Xmas扫描逻辑：
	// - 如果端口关闭，会收到RST响应
	// - 如果端口开放，通常不会有响应（filtered）
	return s.analyzeXmasResponse(ip, port)
}

// buildXmasPacket 构造Xmas数据包（FIN+PSH+URG标志）
func (s *AdvancedScanner) buildXmasPacket(destIP net.IP, destPort int) ([]byte, error) {
	// 获取本地IP
	localIP, err := s.getLocalIP()
	if err != nil {
		return nil, err
	}

	// 随机源端口
	srcPort := 32768 + rand.Intn(32767)

	// 构造IP头 (20字节)
	ipHeader := make([]byte, 20)
	ipHeader[0] = 0x45                   // 版本(4) + 头长度(5)
	ipHeader[1] = 0x00                   // 服务类型
	ipHeader[2] = 0x00                   // 总长度高字节
	ipHeader[3] = 0x28                   // 总长度低字节 (40字节)
	ipHeader[4] = 0x00                   // 标识高字节
	ipHeader[5] = 0x00                   // 标识低字节
	ipHeader[6] = 0x40                   // 标志位
	ipHeader[7] = 0x00                   // 片偏移
	ipHeader[8] = 0x40                   // TTL
	ipHeader[9] = 0x06                   // 协议 (TCP)
	ipHeader[10] = 0x00                  // 校验和高字节
	ipHeader[11] = 0x00                  // 校验和低字节
	copy(ipHeader[12:16], localIP.To4()) // 源IP
	copy(ipHeader[16:20], destIP.To4())  // 目标IP

	// 计算IP头校验和
	s.calculateChecksum(ipHeader[:20])

	// 构造TCP头 (20字节) - Xmas标志 (FIN+PSH+URG)
	tcpHeader := make([]byte, 20)
	tcpHeader[0] = byte(srcPort >> 8)    // 源端口高字节
	tcpHeader[1] = byte(srcPort & 0xff)  // 源端口低字节
	tcpHeader[2] = byte(destPort >> 8)   // 目标端口高字节
	tcpHeader[3] = byte(destPort & 0xff) // 目标端口低字节
	tcpHeader[4] = 0x00                  // 序列号
	tcpHeader[5] = 0x00
	tcpHeader[6] = 0x00
	tcpHeader[7] = 0x01
	tcpHeader[8] = 0x00 // 确认号
	tcpHeader[9] = 0x00
	tcpHeader[10] = 0x00
	tcpHeader[11] = 0x00
	tcpHeader[12] = 0x50 // 头长度(5) + 保留位
	tcpHeader[13] = 0x29 // 标志位 (FIN+PSH+URG = 0x01+0x08+0x20)
	tcpHeader[14] = 0x20 // 窗口大小高字节
	tcpHeader[15] = 0x00 // 窗口大小低字节
	tcpHeader[16] = 0x00 // 校验和高字节
	tcpHeader[17] = 0x00 // 校验和低字节
	tcpHeader[18] = 0x00 // 紧急指针高字节
	tcpHeader[19] = 0x00 // 紧急指针低字节

	// 计算TCP校验和
	s.calculateTCPChecksum(tcpHeader, localIP.To4(), destIP.To4())

	// 合并IP头和TCP头
	packet := make([]byte, 40)
	copy(packet[:20], ipHeader)
	copy(packet[20:], tcpHeader)

	return packet, nil
}

// analyzeXmasResponse 分析Xmas扫描响应
func (s *AdvancedScanner) analyzeXmasResponse(ip net.IP, port int) (string, error) {
	// Xmas扫描：如果端口关闭会收到RST，如果开放通常无响应
	status, err := s.listenForTCPResponse(ip, port, 500*time.Millisecond, 0x04) // 监听RST
	if err != nil {
		// 如果监听失败，回退到快速连接测试
		global.LOG.Debug("Xmas响应监听失败，回退到连接测试", zap.Error(err))
		return s.quickConnectTest(ip, port)
	}

	// Xmas扫描逻辑：收到RST表示关闭，无响应可能表示开放或过滤
	if status == "closed" {
		return "closed", nil
	}

	// 无响应时，尝试快速连接确认
	quickStatus, _ := s.quickConnectTest(ip, port)
	if quickStatus == "open" {
		return "open", nil
	}

	return "filtered", nil
}
