package portscanner

import (
	"fmt"
	"net"
	"strconv"
	"strings"
	"syscall"
	"time"
)

// AdvancedScanner 高级扫描器
type AdvancedScanner struct {
	timeout time.Duration
	retries int
}

// NewAdvancedScanner 创建高级扫描器
func NewAdvancedScanner(timeout time.Duration, retries int) *AdvancedScanner {
	return &AdvancedScanner{
		timeout: timeout,
		retries: retries,
	}
}

// calculateTCPChecksum 计算TCP校验和
func (s *AdvancedScanner) calculateTCPChecksum(tcpHeader []byte, srcIP, destIP []byte) {
	// 构造伪头部
	pseudoHeader := make([]byte, 12)
	copy(pseudoHeader[0:4], srcIP)
	copy(pseudoHeader[4:8], destIP)
	pseudoHeader[8] = 0x00
	pseudoHeader[9] = 0x06 // TCP协议
	pseudoHeader[10] = 0x00
	pseudoHeader[11] = 0x14 // TCP头长度

	// 合并伪头部和TCP头
	data := append(pseudoHeader, tcpHeader...)

	var sum uint32
	for i := 0; i < len(data)-1; i += 2 {
		sum += uint32(data[i])<<8 + uint32(data[i+1])
	}

	if len(data)%2 == 1 {
		sum += uint32(data[len(data)-1]) << 8
	}

	for sum>>16 != 0 {
		sum = (sum & 0xffff) + (sum >> 16)
	}

	checksum := ^sum
	tcpHeader[16] = byte(checksum >> 8)
	tcpHeader[17] = byte(checksum & 0xff)
}

// getLocalIP 获取本地IP地址
func (s *AdvancedScanner) getLocalIP() (net.IP, error) {
	conn, err := net.Dial("udp", "*******:80")
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP, nil
}

// quickConnectTest 快速连接测试
func (s *AdvancedScanner) quickConnectTest(ip net.IP, port int) (string, error) {
	address := net.JoinHostPort(ip.String(), strconv.Itoa(port))
	conn, err := net.DialTimeout("tcp", address, 100*time.Millisecond)
	if err == nil {
		conn.Close()
		return "open", nil
	}

	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		return "filtered", nil
	}

	return "closed", nil
}

// listenForSYNResponse 监听SYN扫描响应
func (s *AdvancedScanner) listenForSYNResponse(ip net.IP, port int, timeout time.Duration) (string, error) {
	// 创建原始套接字用于监听响应
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return "", fmt.Errorf("创建监听套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 设置接收超时
	tv := syscall.Timeval{
		Sec:  int64(timeout.Seconds()),
		Usec: int32(timeout.Nanoseconds() % 1e9 / 1e3),
	}
	err = syscall.SetsockoptTimeval(fd, syscall.SOL_SOCKET, syscall.SO_RCVTIMEO, &tv)
	if err != nil {
		return "", fmt.Errorf("设置接收超时失败: %v", err)
	}

	// 监听响应
	buffer := make([]byte, 1500) // 以太网MTU
	startTime := time.Now()

	for time.Since(startTime) < timeout {
		n, _, err := syscall.Recvfrom(fd, buffer, 0)
		if err != nil {
			if errno, ok := err.(syscall.Errno); ok {
				if errno == syscall.EAGAIN || errno == syscall.EWOULDBLOCK {
					continue // 超时，继续监听
				}
			}
			return "", fmt.Errorf("接收数据失败: %v", err)
		}

		if n < 40 { // IP头(20) + TCP头(20)
			continue
		}

		// 解析IP头
		if buffer[0]>>4 != 4 { // 检查IP版本
			continue
		}

		ipHeaderLen := int(buffer[0]&0x0F) * 4
		if n < ipHeaderLen+20 { // IP头 + TCP头
			continue
		}

		// 检查源IP是否匹配
		srcIP := net.IPv4(buffer[12], buffer[13], buffer[14], buffer[15])
		if !srcIP.Equal(ip) {
			continue
		}

		// 解析TCP头
		tcpHeader := buffer[ipHeaderLen:]
		srcPort := int(tcpHeader[0])<<8 | int(tcpHeader[1])

		if srcPort != port {
			continue
		}

		// 检查TCP标志
		flags := tcpHeader[13]

		// SYN+ACK = 端口开放
		if flags&0x12 == 0x12 { // SYN(0x02) + ACK(0x10)
			return "open", nil
		}

		// RST = 端口关闭
		if flags&0x04 == 0x04 { // RST(0x04)
			return "closed", nil
		}
	}

	// 超时，可能被过滤
	return "filtered", nil
}

// listenForTCPResponse 监听TCP响应（通用方法）
func (s *AdvancedScanner) listenForTCPResponse(ip net.IP, port int, timeout time.Duration, expectedFlags byte) (string, error) {
	// 创建原始套接字用于监听响应
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return "", fmt.Errorf("创建监听套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 设置接收超时
	tv := syscall.Timeval{
		Sec:  int64(timeout.Seconds()),
		Usec: int32(timeout.Nanoseconds() % 1e9 / 1e3),
	}
	err = syscall.SetsockoptTimeval(fd, syscall.SOL_SOCKET, syscall.SO_RCVTIMEO, &tv)
	if err != nil {
		return "", fmt.Errorf("设置接收超时失败: %v", err)
	}

	// 监听响应
	buffer := make([]byte, 1500)
	startTime := time.Now()

	for time.Since(startTime) < timeout {
		n, _, err := syscall.Recvfrom(fd, buffer, 0)
		if err != nil {
			if errno, ok := err.(syscall.Errno); ok {
				if errno == syscall.EAGAIN || errno == syscall.EWOULDBLOCK {
					continue
				}
			}
			return "", fmt.Errorf("接收数据失败: %v", err)
		}

		if n < 40 {
			continue
		}

		// 解析IP头
		if buffer[0]>>4 != 4 {
			continue
		}

		ipHeaderLen := int(buffer[0]&0x0F) * 4
		if n < ipHeaderLen+20 {
			continue
		}

		// 检查源IP
		srcIP := net.IPv4(buffer[12], buffer[13], buffer[14], buffer[15])
		if !srcIP.Equal(ip) {
			continue
		}

		// 解析TCP头
		tcpHeader := buffer[ipHeaderLen:]
		srcPort := int(tcpHeader[0])<<8 | int(tcpHeader[1])

		if srcPort != port {
			continue
		}

		// 检查TCP标志
		flags := tcpHeader[13]

		// RST响应表示端口关闭
		if flags&0x04 == 0x04 {
			return "closed", nil
		}

		// 如果收到期望的标志，进一步分析
		if expectedFlags != 0 && flags&expectedFlags == expectedFlags {
			return "open", nil
		}
	}

	// 超时，可能被过滤或开放
	return "filtered", nil
}

// StealthScan 隐蔽扫描
func (s *AdvancedScanner) StealthScan(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   "closed",
	}

	// 隐蔽扫描策略：
	// 1. 随机延迟
	// 2. 降低扫描速度
	// 3. 使用不同的源端口

	// 随机延迟 100-500ms
	delay := time.Duration(100+target.Port%400) * time.Millisecond
	time.Sleep(delay)

	// 使用较长的超时时间，模拟正常连接
	stealthTimeout := s.timeout * 2
	if stealthTimeout > 10*time.Second {
		stealthTimeout = 10 * time.Second
	}

	start := time.Now()
	address := net.JoinHostPort(target.IP.String(), strconv.Itoa(target.Port))

	// 尝试连接
	conn, err := net.DialTimeout("tcp", address, stealthTimeout)
	result.ResponseTime = time.Since(start)

	if err == nil {
		// 连接成功，但要优雅关闭以减少日志记录
		time.Sleep(50 * time.Millisecond) // 短暂保持连接
		conn.Close()
		result.Status = "open"
	} else {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			result.Status = "filtered"
		} else {
			result.Status = "closed"
		}
		result.Error = err
	}

	return result
}



// hasRawSocketPermission 检查是否有原始套接字权限
func (s *AdvancedScanner) hasRawSocketPermission() bool {
	// 尝试创建原始套接字来检查权限
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_TCP)
	if err != nil {
		return false
	}
	syscall.Close(fd)
	return true
}

// fallbackTCPScan 回退到TCP连接扫描
func (s *AdvancedScanner) fallbackTCPScan(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   "closed",
	}

	address := net.JoinHostPort(target.IP.String(), strconv.Itoa(target.Port))
	start := time.Now()

	conn, err := net.DialTimeout("tcp", address, s.timeout)
	result.ResponseTime = time.Since(start)

	if err == nil {
		conn.Close()
		result.Status = "open"
	} else {
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			result.Status = "filtered"
		} else {
			result.Status = "closed"
		}
		result.Error = err
	}

	return result
}

// GetScannerCapabilities 获取扫描器能力
func (s *AdvancedScanner) GetScannerCapabilities() map[string]bool {
	hasRoot := s.hasRawSocketPermission()

	return map[string]bool{
		"tcp_connect": true,
		"tcp_syn":     hasRoot,
		"tcp_fin":     hasRoot,
		"tcp_xmas":    hasRoot,
		"tcp_null":    hasRoot,
		"tcp_ack":     hasRoot,
		"tcp_window":  hasRoot,
		"udp":         true,
		"stealth":     true,
	}
}

// ValidateScanType 验证扫描类型是否支持
func (s *AdvancedScanner) ValidateScanType(scanType string) error {
	capabilities := s.GetScannerCapabilities()

	switch strings.ToLower(scanType) {
	case "tcp", "tcp_connect":
		return nil
	case "syn", "tcp_syn":
		if !capabilities["tcp_syn"] {
			return fmt.Errorf("SYN扫描需要root权限")
		}
		return nil
	case "fin", "tcp_fin":
		if !capabilities["tcp_fin"] {
			return fmt.Errorf("FIN扫描需要root权限")
		}
		return nil
	case "xmas", "tcp_xmas":
		if !capabilities["tcp_xmas"] {
			return fmt.Errorf("Xmas扫描需要root权限")
		}
		return nil
	case "null", "tcp_null":
		if !capabilities["tcp_null"] {
			return fmt.Errorf("Null扫描需要root权限")
		}
		return nil
	case "ack", "tcp_ack":
		if !capabilities["tcp_ack"] {
			return fmt.Errorf("ACK扫描需要root权限")
		}
		return nil
	case "window", "tcp_window":
		if !capabilities["tcp_window"] {
			return fmt.Errorf("Window扫描需要root权限")
		}
		return nil
	case "udp":
		return nil
	case "stealth":
		return nil
	case "both":
		return nil
	default:
		return fmt.Errorf("不支持的扫描类型: %s", scanType)
	}
}

// calculateChecksum 计算校验和
func (s *AdvancedScanner) calculateChecksum(data []byte) {
	var sum uint32

	// 16位对齐求和
	for i := 0; i < len(data)-1; i += 2 {
		sum += uint32(data[i])<<8 + uint32(data[i+1])
	}

	// 处理奇数长度
	if len(data)%2 == 1 {
		sum += uint32(data[len(data)-1]) << 8
	}

	// 处理进位
	for sum>>16 != 0 {
		sum = (sum & 0xffff) + (sum >> 16)
	}

	// 取反
	checksum := ^sum
	data[10] = byte(checksum >> 8)
	data[11] = byte(checksum & 0xff)
}
