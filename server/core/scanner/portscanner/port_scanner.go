package portscanner

import (
	"context"
	"fmt"
	"math/rand"
	"net"
	"strconv"
	"strings"
	"sync"
	"sync/atomic"
	"time"

	"server/global"
	"server/model/scan"

	"go.uber.org/zap"
)

// PortScanner 端口扫描器
type PortScanner struct {
	batchSize       int
	timeout         time.Duration
	retries         int
	workers         int
	ctx             context.Context
	cancel          context.CancelFunc
	mu              sync.RWMutex
	running         bool
	advancedScanner *AdvancedScanner
}

// ScanTarget 扫描目标
type ScanTarget struct {
	IP       net.IP
	Port     int
	Protocol string
	ScanType string // 扫描类型：tcp, syn, udp, stealth等
}

// ScanResult 扫描结果
type ScanResult struct {
	Target       ScanTarget
	Status       string
	ResponseTime time.Duration
	Service      string
	Banner       string
	Error        error
	ScanTime     time.Time
	Retries      int
}

// ScanProgress 扫描进度
type ScanProgress struct {
	TaskID         uint64
	TotalTargets   int
	ScannedTargets int
	CurrentTarget  string
	Progress       float64
	OpenPorts      int
	ClosedPorts    int
	FilteredPorts  int
	ErrorPorts     int
	ElapsedTime    time.Duration
}

// ProgressCallback 进度回调函数
type ProgressCallback func(progress ScanProgress)

// ResultCallback 结果回调函数
type ResultCallback func(result ScanResult)

// NewPortScanner 创建新的端口扫描器
func NewPortScanner(batchSize int, timeout time.Duration, retries int) *PortScanner {
	// 智能计算工作协程数，避免过高并发导致漏扫
	workers := batchSize / 10 // 降低并发比例
	if workers < 50 {
		workers = 50 // 最小工作协程数
	}
	if workers > 500 {
		workers = 500 // 限制最大工作协程数，避免系统资源耗尽
	}

	global.LOG.Info("创建端口扫描器",
		zap.Int("batchSize", batchSize),
		zap.Int("workers", workers),
		zap.Duration("timeout", timeout),
		zap.Int("retries", retries))

	return &PortScanner{
		batchSize:       batchSize,
		timeout:         timeout,
		retries:         retries,
		workers:         workers,
		running:         false,
		advancedScanner: NewAdvancedScanner(timeout, retries),
	}
}

// Start 启动扫描器
func (s *PortScanner) Start() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.running {
		return
	}

	s.ctx, s.cancel = context.WithCancel(context.Background())
	s.running = true
}

// Stop 停止扫描器
func (s *PortScanner) Stop() {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.running {
		return
	}

	if s.cancel != nil {
		s.cancel()
	}
	s.running = false
}

// IsRunning 检查是否正在运行
func (s *PortScanner) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.running
}

// CompletionCallback 扫描完成回调
type CompletionCallback func(taskID uint64, totalTargets int, scannedTargets int64, elapsedTime time.Duration)

// ScanAsync 异步扫描
func (s *PortScanner) ScanAsync(taskID uint64, targets []ScanTarget, progressCallback ProgressCallback, resultCallback ResultCallback) error {
	return s.ScanAsyncWithCompletion(taskID, targets, progressCallback, resultCallback, nil)
}

// ScanAsyncWithCompletion 异步扫描（带完成回调）
func (s *PortScanner) ScanAsyncWithCompletion(taskID uint64, targets []ScanTarget, progressCallback ProgressCallback, resultCallback ResultCallback, completionCallback CompletionCallback) error {
	if !s.IsRunning() {
		return fmt.Errorf("扫描器未启动")
	}

	global.LOG.Info("开始端口扫描",
		zap.Uint64("taskID", taskID),
		zap.Int("targets", len(targets)),
		zap.Int("batchSize", s.batchSize),
		zap.Duration("timeout", s.timeout))

	go s.scan(taskID, targets, progressCallback, resultCallback, completionCallback)
	return nil
}

// scan 执行扫描
func (s *PortScanner) scan(taskID uint64, targets []ScanTarget, progressCallback ProgressCallback, resultCallback ResultCallback, completionCallback CompletionCallback) {
	startTime := time.Now()
	totalTargets := len(targets)
	var scannedTargets int64
	var openPorts, closedPorts, filteredPorts, errorPorts int64

	// 创建工作池
	targetChan := make(chan ScanTarget, s.batchSize)
	resultChan := make(chan ScanResult, s.batchSize)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < s.workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			s.worker(targetChan, resultChan)
		}()
	}

	// 启动结果处理协程
	go func() {
		for result := range resultChan {
			// 更新统计
			atomic.AddInt64(&scannedTargets, 1)
			switch result.Status {
			case scan.PortStatusOpen:
				atomic.AddInt64(&openPorts, 1)
			case scan.PortStatusClosed:
				atomic.AddInt64(&closedPorts, 1)
			case scan.PortStatusFiltered:
				atomic.AddInt64(&filteredPorts, 1)
			default:
				atomic.AddInt64(&errorPorts, 1)
			}

			// 调用结果回调
			if resultCallback != nil {
				resultCallback(result)
			}

			// 调用进度回调
			if progressCallback != nil {
				scanned := atomic.LoadInt64(&scannedTargets)
				progress := ScanProgress{
					TaskID:         taskID,
					TotalTargets:   totalTargets,
					ScannedTargets: int(scanned),
					Progress:       float64(scanned) / float64(totalTargets) * 100,
					OpenPorts:      int(atomic.LoadInt64(&openPorts)),
					ClosedPorts:    int(atomic.LoadInt64(&closedPorts)),
					FilteredPorts:  int(atomic.LoadInt64(&filteredPorts)),
					ErrorPorts:     int(atomic.LoadInt64(&errorPorts)),
					ElapsedTime:    time.Since(startTime),
				}
				progressCallback(progress)
			}
		}
	}()

	// 发送扫描目标
	go func() {
		defer close(targetChan)
		for _, target := range targets {
			select {
			case <-s.ctx.Done():
				return
			case targetChan <- target:
			}
		}
	}()

	// 等待所有工作协程完成
	wg.Wait()
	close(resultChan)

	elapsedTime := time.Since(startTime)

	global.LOG.Info("端口扫描完成",
		zap.Uint64("taskID", taskID),
		zap.Int("totalTargets", totalTargets),
		zap.Int64("scannedTargets", scannedTargets),
		zap.Duration("elapsedTime", elapsedTime))

	// 调用完成回调
	if completionCallback != nil {
		completionCallback(taskID, totalTargets, scannedTargets, elapsedTime)
	}
}

// worker 工作协程
func (s *PortScanner) worker(targetChan <-chan ScanTarget, resultChan chan<- ScanResult) {
	for target := range targetChan {
		select {
		case <-s.ctx.Done():
			return
		default:
			result := s.scanTarget(target)
			select {
			case <-s.ctx.Done():
				return
			case resultChan <- result:
			}
		}
	}
}

// scanTarget 扫描单个目标
func (s *PortScanner) scanTarget(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   scan.PortStatusClosed,
	}

	// 根据扫描类型选择扫描方法
	switch strings.ToLower(target.ScanType) {
	case "tcp", "tcp_connect", "":
		result = s.scanTCP(target)
	case "syn", "tcp_syn":
		result = s.advancedScanner.SYNScan(target)
	case "fin", "tcp_fin":
		result = s.advancedScanner.FINScan(target)
	case "xmas", "tcp_xmas":
		result = s.advancedScanner.XmasScan(target)
	case "null", "tcp_null":
		result = s.advancedScanner.NullScan(target)
	case "ack", "tcp_ack":
		result = s.advancedScanner.ACKScan(target)
	case "window", "tcp_window":
		result = s.advancedScanner.WindowScan(target)
	case "stealth":
		result = s.advancedScanner.StealthScan(target)
	case "udp":
		result = s.scanUDP(target)
	default:
		// 回退到协议判断
		switch strings.ToLower(target.Protocol) {
		case "tcp":
			result = s.scanTCP(target)
		case "udp":
			result = s.scanUDP(target)
		default:
			result.Status = scan.PortStatusError
			result.Error = fmt.Errorf("不支持的扫描类型: %s", target.ScanType)
		}
	}

	// 尝试获取服务信息
	if result.Status == scan.PortStatusOpen {
		result.Service = scan.GetServiceName(target.Port)
		// 这里可以添加横幅抓取逻辑
	}

	return result
}

// scanTCP TCP端口扫描
func (s *PortScanner) scanTCP(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   scan.PortStatusClosed,
	}

	address := net.JoinHostPort(target.IP.String(), strconv.Itoa(target.Port))

	for attempt := 0; attempt <= s.retries; attempt++ {
		start := time.Now()

		conn, err := net.DialTimeout("tcp", address, s.timeout)
		result.ResponseTime = time.Since(start)
		result.Retries = attempt

		if err == nil {
			// 尝试读取横幅信息
			conn.SetReadDeadline(time.Now().Add(time.Millisecond * 500))
			banner := make([]byte, 1024)
			n, _ := conn.Read(banner)
			if n > 0 {
				result.Banner = string(banner[:n])
			}
			conn.Close()
			result.Status = scan.PortStatusOpen
			return result
		}

		// 检查错误类型
		if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
			result.Status = scan.PortStatusFiltered
			result.Error = err
		} else {
			result.Status = scan.PortStatusClosed
			result.Error = err
		}

		// 如果是最后一次尝试，退出循环
		if attempt == s.retries {
			break
		}

		// 根据尝试次数调整延迟时间
		delay := time.Duration(attempt+1) * 20 * time.Millisecond
		if delay > 100*time.Millisecond {
			delay = 100 * time.Millisecond
		}
		time.Sleep(delay)
	}

	return result
}

// scanUDP UDP端口扫描
func (s *PortScanner) scanUDP(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   scan.PortStatusFiltered, // UDP默认为过滤状态
	}

	address := net.JoinHostPort(target.IP.String(), strconv.Itoa(target.Port))

	for attempt := 0; attempt <= s.retries; attempt++ {
		start := time.Now()

		conn, err := net.DialTimeout("udp", address, s.timeout)
		if err != nil {
			result.ResponseTime = time.Since(start)
			result.Error = err
			result.Retries = attempt
			continue
		}

		// 发送探测数据
		_, err = conn.Write([]byte("test"))
		if err != nil {
			conn.Close()
			result.ResponseTime = time.Since(start)
			result.Error = err
			result.Retries = attempt
			continue
		}

		// 尝试读取响应
		conn.SetReadDeadline(time.Now().Add(s.timeout))
		buffer := make([]byte, 1024)
		n, err := conn.Read(buffer)
		conn.Close()

		result.ResponseTime = time.Since(start)
		result.Retries = attempt

		if err == nil && n > 0 {
			result.Status = scan.PortStatusOpen
			result.Banner = string(buffer[:n])
			return result
		}

		// UDP扫描比较困难，通常假设端口是开放的
		if attempt == s.retries {
			result.Status = scan.PortStatusFiltered
		}
	}

	return result
}

// GenerateTargets 生成扫描目标
func GenerateTargets(ips []net.IP, ports []int, protocol string) []ScanTarget {
	var targets []ScanTarget

	for _, ip := range ips {
		for _, port := range ports {
			targets = append(targets, ScanTarget{
				IP:       ip,
				Port:     port,
				Protocol: protocol,
				ScanType: protocol, // 默认使用协议作为扫描类型
			})
		}
	}

	return targets
}

// ShuffleTargets 随机打乱目标顺序
func ShuffleTargets(targets []ScanTarget) {
	// 使用新的随机数生成器
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	r.Shuffle(len(targets), func(i, j int) {
		targets[i], targets[j] = targets[j], targets[i]
	})
}

// GenerateTargetsWithScanType 生成带扫描类型的扫描目标
func GenerateTargetsWithScanType(ips []net.IP, ports []int, protocol string, scanType string) []ScanTarget {
	var targets []ScanTarget

	for _, ip := range ips {
		for _, port := range ports {
			targets = append(targets, ScanTarget{
				IP:       ip,
				Port:     port,
				Protocol: protocol,
				ScanType: scanType,
			})
		}
	}

	return targets
}
