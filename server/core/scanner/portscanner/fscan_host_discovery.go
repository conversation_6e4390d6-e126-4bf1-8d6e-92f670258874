package portscanner

import (
	"bytes"
	"context"
	"net"
	"os/exec"
	"runtime"
	"strconv"
	"strings"
	"sync"
	"time"

	"server/global"

	"go.uber.org/zap"
	"golang.org/x/net/icmp"
	"golang.org/x/net/ipv4"
)

// FScanHostDiscovery 基于fscan的高性能主机发现器
type FScanHostDiscovery struct {
	aliveHosts []string
	existHosts map[string]struct{}
	livewg     sync.WaitGroup
	mu         sync.Mutex
	ctx        context.Context
	cancel     context.CancelFunc
	options    DiscoveryOptions
}

// NewFScanHostDiscovery 创建基于fscan的主机发现器
func NewFScanHostDiscovery(options DiscoveryOptions) *FScanHostDiscovery {
	ctx, cancel := context.WithCancel(context.Background())
	return &FScanHostDiscovery{
		aliveHosts: make([]string, 0),
		existHosts: make(map[string]struct{}),
		ctx:        ctx,
		cancel:     cancel,
		options:    options,
	}
}

// Start 启动发现器
func (f *FScanHostDiscovery) Start() {
	global.LOG.Debug("FScan主机发现器启动")
}

// Stop 停止发现器
func (f *FScanHostDiscovery) Stop() {
	if f.cancel != nil {
		f.cancel()
	}
	global.LOG.Debug("FScan主机发现器停止")
}

// DiscoverHosts 发现存活主机 - 使用fscan的高效算法
func (f *FScanHostDiscovery) DiscoverHosts(targets []net.IP, progressCallback func(HostStatus)) ([]net.IP, error) {
	// 转换为字符串列表
	hostslist := make([]string, len(targets))
	for i, ip := range targets {
		hostslist[i] = ip.String()
	}

	global.LOG.Info("开始FScan主机发现",
		zap.Int("totalTargets", len(hostslist)),
		zap.Bool("enablePing", f.options.EnablePing),
		zap.Bool("noPing", f.options.NoPing))

	// 重置状态
	f.aliveHosts = make([]string, 0)
	f.existHosts = make(map[string]struct{})

	// 使用fscan的CheckLive逻辑
	aliveList := f.checkLive(hostslist, progressCallback)

	// 转换回IP列表
	result := make([]net.IP, 0, len(aliveList))
	for _, ipStr := range aliveList {
		if ip := net.ParseIP(ipStr); ip != nil {
			result = append(result, ip)
		}
	}

	global.LOG.Info("FScan主机发现完成",
		zap.Int("totalTargets", len(hostslist)),
		zap.Int("aliveTargets", len(result)),
		zap.Float64("aliveRatio", float64(len(result))/float64(len(hostslist))*100))

	return result, nil
}

// checkLive 检测主机存活状态 - 基于fscan的CheckLive
func (f *FScanHostDiscovery) checkLive(hostslist []string, progressCallback func(HostStatus)) []string {
	// 创建主机通道
	chanHosts := make(chan string, len(hostslist))

	// 处理存活主机
	go f.handleAliveHosts(chanHosts, hostslist, progressCallback)

	// 根据配置选择检测方式
	if f.options.NoPing {
		// NoPing模式，直接返回所有主机
		for _, host := range hostslist {
			f.livewg.Add(1)
			chanHosts <- host
		}
	} else if f.options.EnablePing {
		// 使用ping方式探测
		f.runPing(hostslist, chanHosts)
	} else {
		// 使用ICMP方式探测
		f.probeWithICMP(hostslist, chanHosts)
	}

	// 等待所有检测完成
	f.livewg.Wait()
	close(chanHosts)

	return f.aliveHosts
}

// handleAliveHosts 处理存活主机 - 基于fscan逻辑
func (f *FScanHostDiscovery) handleAliveHosts(chanHosts chan string, hostslist []string, progressCallback func(HostStatus)) {
	processed := 0
	total := len(hostslist)

	for host := range chanHosts {
		f.livewg.Done()
		processed++

		// 检查是否已存在
		f.mu.Lock()
		if _, exists := f.existHosts[host]; !exists {
			f.existHosts[host] = struct{}{}
			f.aliveHosts = append(f.aliveHosts, host)

			// 调用进度回调
			if progressCallback != nil {
				ip := net.ParseIP(host)
				if ip != nil {
					status := HostStatus{
						IP:        ip,
						IsAlive:   true,
						Method:    "fscan",
						CheckTime: time.Now(),
					}
					progressCallback(status)
				}
			}

			global.LOG.Debug("发现存活主机",
				zap.String("host", host),
				zap.Int("processed", processed),
				zap.Int("total", total),
				zap.Int("alive", len(f.aliveHosts)))
		}
		f.mu.Unlock()
	}
}

// runPing 使用ping命令探测 - 基于fscan的ping逻辑
func (f *FScanHostDiscovery) runPing(hostslist []string, chanHosts chan string) {
	var wg sync.WaitGroup
	limiter := make(chan struct{}, f.options.Workers)
	if f.options.Workers <= 0 {
		limiter = make(chan struct{}, 100) // 默认100个并发
	}

	for _, host := range hostslist {
		wg.Add(1)
		f.livewg.Add(1)

		go func(h string) {
			defer wg.Done()
			limiter <- struct{}{}
			defer func() { <-limiter }()

			if f.execPing(h) {
				chanHosts <- h
			} else {
				f.livewg.Done()
			}
		}(host)
	}

	wg.Wait()
}

// execPing 执行ping命令 - 基于fscan的ping实现
func (f *FScanHostDiscovery) execPing(host string) bool {
	timeout := f.options.Timeout
	if timeout <= 0 {
		timeout = 800 // 默认800ms
	}

	var cmd *exec.Cmd
	switch runtime.GOOS {
	case "windows":
		cmd = exec.Command("ping", "-n", "1", "-w", strconv.Itoa(timeout), host)
	case "darwin":
		cmd = exec.Command("ping", "-c", "1", "-W", strconv.Itoa(timeout), host)
	default: // linux
		cmd = exec.Command("ping", "-c", "1", "-W", strconv.Itoa(timeout/1000+1), host)
	}

	var out bytes.Buffer
	cmd.Stdout = &out
	err := cmd.Run()

	if err != nil {
		return false
	}

	// 检查ping输出
	output := out.String()
	if runtime.GOOS == "windows" {
		return !strings.Contains(output, "Request timed out") &&
			!strings.Contains(output, "could not find host") &&
			!strings.Contains(output, "Destination host unreachable")
	} else {
		return !strings.Contains(output, "100% packet loss") &&
			!strings.Contains(output, "Host Unreachable") &&
			!strings.Contains(output, "Network is unreachable")
	}
}

// probeWithICMP 使用ICMP探测 - 基于fscan的ICMP实现
func (f *FScanHostDiscovery) probeWithICMP(hostslist []string, chanHosts chan string) {
	// 尝试创建ICMP连接
	conn, err := icmp.ListenPacket("ip4:icmp", "0.0.0.0")
	if err != nil {
		global.LOG.Warn("无法创建ICMP连接，回退到ping模式", zap.Error(err))
		f.runPing(hostslist, chanHosts)
		return
	}
	defer conn.Close()

	var wg sync.WaitGroup
	limiter := make(chan struct{}, f.options.Workers)
	if f.options.Workers <= 0 {
		limiter = make(chan struct{}, 100)
	}

	for _, host := range hostslist {
		wg.Add(1)
		f.livewg.Add(1)

		go func(h string) {
			defer wg.Done()
			limiter <- struct{}{}
			defer func() { <-limiter }()

			if f.sendICMP(conn, h) {
				chanHosts <- h
			} else {
				f.livewg.Done()
			}
		}(host)
	}

	wg.Wait()
}

// sendICMP 发送ICMP包 - 基于fscan的ICMP实现
func (f *FScanHostDiscovery) sendICMP(conn *icmp.PacketConn, host string) bool {
	dst, err := net.ResolveIPAddr("ip4", host)
	if err != nil {
		return false
	}

	// 创建ICMP消息
	message := &icmp.Message{
		Type: ipv4.ICMPTypeEcho,
		Code: 0,
		Body: &icmp.Echo{
			ID:   1,
			Seq:  1,
			Data: []byte("fscan"),
		},
	}

	data, err := message.Marshal(nil)
	if err != nil {
		return false
	}

	// 设置超时
	timeout := time.Duration(f.options.Timeout) * time.Millisecond
	if timeout <= 0 {
		timeout = 800 * time.Millisecond
	}

	// 发送ICMP包
	_, err = conn.WriteTo(data, dst)
	if err != nil {
		return false
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(timeout))

	// 读取响应
	reply := make([]byte, 1500)
	_, _, err = conn.ReadFrom(reply)

	return err == nil
}
