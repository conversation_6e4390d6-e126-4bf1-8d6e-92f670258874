package portscanner

import (
	"context"
	"fmt"
	"net"
	"sync"
	"time"

	"server/global"

	"go.uber.org/zap"
	"golang.org/x/net/icmp"
	"golang.org/x/net/ipv4"
)

// HostDiscovery 主机发现器
type HostDiscovery struct {
	timeout    time.Duration
	workers    int
	enablePing bool
	enableTCP  bool
	tcpPorts   []int // 用于TCP存活检测的端口
	ctx        context.Context
	cancel     context.CancelFunc
}

// HostStatus 主机状态
type HostStatus struct {
	IP        net.IP
	IsAlive   bool
	Method    string        // ping, tcp, none
	RTT       time.Duration // 响应时间
	Error     error
	CheckTime time.Time
}

// DiscoveryOptions 发现选项
type DiscoveryOptions struct {
	EnablePing bool  // 启用ICMP ping
	EnableTCP  bool  // 启用TCP存活检测
	NoPing     bool  // 跳过ping检测，直接认为主机存活
	TCPPorts   []int // TCP检测端口
	Timeout    int   // 超时时间(ms)
	Workers    int   // 并发数
}

// NewHostDiscovery 创建主机发现器
func NewHostDiscovery(options DiscoveryOptions) *HostDiscovery {
	timeout := time.Duration(options.Timeout) * time.Millisecond
	if timeout == 0 {
		timeout = 1000 * time.Millisecond
	}

	workers := options.Workers
	if workers == 0 {
		workers = 100
	}

	tcpPorts := options.TCPPorts
	if len(tcpPorts) == 0 {
		// 默认使用常见端口进行TCP存活检测
		tcpPorts = []int{80, 443, 22, 21, 23, 25, 53, 110, 143, 445,
			139, 135, 3389, 8080, 8443, 53, 123, 161, 162,
			500, 4500, 3306, 1433, 1521, 27017, 6379, 9200,
			5900, 5901, 5902, 5672, 11211, 111, 2049,
		}
	}

	return &HostDiscovery{
		timeout:    timeout,
		workers:    workers,
		enablePing: options.EnablePing && !options.NoPing,
		enableTCP:  options.EnableTCP,
		tcpPorts:   tcpPorts,
	}
}

// Start 启动发现器
func (h *HostDiscovery) Start() {
	h.ctx, h.cancel = context.WithCancel(context.Background())
}

// Stop 停止发现器
func (h *HostDiscovery) Stop() {
	if h.cancel != nil {
		h.cancel()
	}
}

// DiscoverHosts 发现存活主机
func (h *HostDiscovery) DiscoverHosts(targets []net.IP, callback func(HostStatus)) ([]net.IP, error) {
	if h.ctx == nil {
		return nil, fmt.Errorf("发现器未启动")
	}

	global.LOG.Info("开始主机发现",
		zap.Int("targets", len(targets)),
		zap.Bool("enablePing", h.enablePing),
		zap.Bool("enableTCP", h.enableTCP))

	var aliveHosts []net.IP
	var mu sync.Mutex

	// 创建带缓冲的通道，避免阻塞
	targetChan := make(chan net.IP, h.workers)
	resultChan := make(chan HostStatus, h.workers*2)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < h.workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			h.worker(targetChan, resultChan)
		}()
	}

	// 启动结果处理协程
	var resultWg sync.WaitGroup
	resultWg.Add(1)
	go func() {
		defer resultWg.Done()
		for {
			select {
			case <-h.ctx.Done():
				return
			case status, ok := <-resultChan:
				if !ok {
					return
				}

				if callback != nil {
					callback(status)
				}

				if status.IsAlive {
					mu.Lock()
					aliveHosts = append(aliveHosts, status.IP)
					mu.Unlock()
				}
			}
		}
	}()

	// 发送目标
	go func() {
		defer close(targetChan)
		for _, target := range targets {
			select {
			case <-h.ctx.Done():
				return
			case targetChan <- target:
			}
		}
	}()

	// 等待工作协程完成
	wg.Wait()

	// 关闭结果通道
	close(resultChan)

	// 等待结果处理完成
	resultWg.Wait()

	global.LOG.Info("主机发现完成",
		zap.Int("totalTargets", len(targets)),
		zap.Int("aliveHosts", len(aliveHosts)))

	return aliveHosts, nil
}

// worker 工作协程
func (h *HostDiscovery) worker(targetChan <-chan net.IP, resultChan chan<- HostStatus) {
	for target := range targetChan {
		select {
		case <-h.ctx.Done():
			return
		default:
			status := h.checkHost(target)
			select {
			case <-h.ctx.Done():
				return
			case resultChan <- status:
			}
		}
	}
}

// checkHost 检查主机存活状态
func (h *HostDiscovery) checkHost(ip net.IP) HostStatus {
	status := HostStatus{
		IP:        ip,
		IsAlive:   false,
		CheckTime: time.Now(),
	}

	// 如果禁用了所有检测方法，直接认为存活
	if !h.enablePing && !h.enableTCP {
		status.IsAlive = true
		status.Method = "none"
		return status
	}

	// 先尝试TCP连接检测（更可靠）
	if h.enableTCP {
		if h.tcpCheck(ip, &status) {
			return status
		}
	}

	// 再尝试ICMP ping（可能被防火墙阻止）
	if h.enablePing {
		if h.pingHost(ip, &status) {
			return status
		}
	}

	// 如果都失败了，记录详细信息
	status.Method = "failed"
	if h.enablePing && h.enableTCP {
		status.Error = fmt.Errorf("ICMP ping和TCP连接均失败")
	} else if h.enablePing {
		status.Error = fmt.Errorf("ICMP ping失败")
	} else if h.enableTCP {
		status.Error = fmt.Errorf("TCP连接失败")
	}

	return status
}

// pingHost ICMP ping检测
func (h *HostDiscovery) pingHost(ip net.IP, status *HostStatus) bool {
	start := time.Now()

	// 创建ICMP连接
	conn, err := icmp.ListenPacket("ip4:icmp", "0.0.0.0")
	if err != nil {
		// 如果无法创建ICMP连接（权限问题），回退到TCP检测
		status.Error = fmt.Errorf("ICMP权限不足: %v", err)
		return false
	}
	defer conn.Close()

	// 设置超时
	conn.SetDeadline(time.Now().Add(h.timeout))

	// 构造ICMP Echo请求
	message := &icmp.Message{
		Type: ipv4.ICMPTypeEcho,
		Code: 0,
		Body: &icmp.Echo{
			ID:   1,
			Seq:  1,
			Data: []byte("ping"),
		},
	}

	data, err := message.Marshal(nil)
	if err != nil {
		status.Error = err
		return false
	}

	// 发送ping
	_, err = conn.WriteTo(data, &net.IPAddr{IP: ip})
	if err != nil {
		status.Error = err
		return false
	}

	// 接收回复
	reply := make([]byte, 1500)
	_, _, err = conn.ReadFrom(reply)
	if err != nil {
		status.Error = err
		return false
	}

	status.IsAlive = true
	status.Method = "ping"
	status.RTT = time.Since(start)
	return true
}

// tcpCheck TCP连接检测
func (h *HostDiscovery) tcpCheck(ip net.IP, status *HostStatus) bool {
	// 使用更短的超时时间进行快速检测
	quickTimeout := h.timeout / 2
	if quickTimeout < 500*time.Millisecond {
		quickTimeout = 500 * time.Millisecond
	}

	// 优先检测最常见的端口
	priorityPorts := []int{80, 443, 22, 21, 23, 25, 53, 135, 139, 445, 3389}

	// 先检查优先端口
	for _, port := range priorityPorts {
		if h.checkTCPPort(ip, port, quickTimeout, status) {
			return true
		}

		// 检查是否被取消
		select {
		case <-h.ctx.Done():
			return false
		default:
		}
	}

	// 如果优先端口都失败，再检查其他端口
	for _, port := range h.tcpPorts {
		// 跳过已经检查过的优先端口
		if h.isInPriorityPorts(port, priorityPorts) {
			continue
		}

		if h.checkTCPPort(ip, port, quickTimeout, status) {
			return true
		}

		// 检查是否被取消
		select {
		case <-h.ctx.Done():
			return false
		default:
		}
	}

	status.Method = "tcp"
	status.Error = fmt.Errorf("所有TCP端口均无响应")
	return false
}

// checkTCPPort 检查单个TCP端口
func (h *HostDiscovery) checkTCPPort(ip net.IP, port int, timeout time.Duration, status *HostStatus) bool {
	start := time.Now()
	address := fmt.Sprintf("%s:%d", ip.String(), port)

	conn, err := net.DialTimeout("tcp", address, timeout)
	if err == nil {
		conn.Close()
		status.IsAlive = true
		status.Method = fmt.Sprintf("tcp:%d", port)
		status.RTT = time.Since(start)
		return true
	}

	return false
}

// isInPriorityPorts 检查端口是否在优先列表中
func (h *HostDiscovery) isInPriorityPorts(port int, priorityPorts []int) bool {
	for _, p := range priorityPorts {
		if p == port {
			return true
		}
	}
	return false
}

// QuickDiscovery 快速发现（仅ping）
func QuickDiscovery(targets []net.IP, timeout time.Duration) []net.IP {
	discovery := NewHostDiscovery(DiscoveryOptions{
		EnablePing: true,
		EnableTCP:  false,
		Timeout:    int(timeout.Milliseconds()),
		Workers:    50,
	})

	discovery.Start()
	defer discovery.Stop()

	aliveHosts, _ := discovery.DiscoverHosts(targets, nil)
	return aliveHosts
}
