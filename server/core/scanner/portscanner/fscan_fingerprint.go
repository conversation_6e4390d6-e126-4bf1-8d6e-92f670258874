package portscanner

import (
	"bufio"
	"net"
	"regexp"
	"strconv"
	"strings"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// FScanFingerprint 基于fscan的服务指纹识别
type FScanFingerprint struct {
	probes map[string]ProbeConfig
}

// ProbeConfig 探测配置
type ProbeConfig struct {
	Name     string
	Data     string
	Ports    []int
	Patterns []PatternMatch
}

// PatternMatch 模式匹配
type PatternMatch struct {
	Service string
	Pattern *regexp.Regexp
	Version string
}

// FingerprintResult 指纹识别结果
type FingerprintResult struct {
	Service    string
	Version    string
	Product    string
	ExtraInfo  string
	Hostname   string
	OS         string
	DeviceType string
	Confidence int
}

// NewFScanFingerprint 创建指纹识别器
func NewFScanFingerprint() *FScanFingerprint {
	fp := &FScanFingerprint{
		probes: make(map[string]ProbeConfig),
	}
	fp.initProbes()
	return fp
}

// initProbes 初始化探测配置 - 基于fscan的nmap-service-probes
func (f *FScanFingerprint) initProbes() {
	// HTTP探测
	f.probes["HTTP"] = ProbeConfig{
		Name:  "HTTP",
		Data:  "GET / HTTP/1.1\r\nHost: {host}\r\nUser-Agent: Mozilla/5.0\r\nConnection: close\r\n\r\n",
		Ports: []int{80, 8080, 8000, 8888, 8081, 9000, 9090},
		Patterns: []PatternMatch{
			{
				Service: "http",
				Pattern: regexp.MustCompile(`(?i)HTTP/1\.[01] \d+ `),
				Version: "HTTP",
			},
			{
				Service: "nginx",
				Pattern: regexp.MustCompile(`(?i)Server: nginx/([^\r\n]+)`),
				Version: "$1",
			},
			{
				Service: "apache",
				Pattern: regexp.MustCompile(`(?i)Server: Apache/([^\r\n]+)`),
				Version: "$1",
			},
			{
				Service: "iis",
				Pattern: regexp.MustCompile(`(?i)Server: Microsoft-IIS/([^\r\n]+)`),
				Version: "$1",
			},
		},
	}

	// SSH探测
	f.probes["SSH"] = ProbeConfig{
		Name:  "SSH",
		Data:  "",
		Ports: []int{22},
		Patterns: []PatternMatch{
			{
				Service: "ssh",
				Pattern: regexp.MustCompile(`SSH-([^\r\n]+)`),
				Version: "$1",
			},
			{
				Service: "openssh",
				Pattern: regexp.MustCompile(`OpenSSH_([^\r\n\s]+)`),
				Version: "$1",
			},
		},
	}

	// FTP探测
	f.probes["FTP"] = ProbeConfig{
		Name:  "FTP",
		Data:  "",
		Ports: []int{21},
		Patterns: []PatternMatch{
			{
				Service: "ftp",
				Pattern: regexp.MustCompile(`220[^\r\n]*`),
				Version: "FTP",
			},
			{
				Service: "vsftpd",
				Pattern: regexp.MustCompile(`220.*vsftpd ([^\r\n\s]+)`),
				Version: "$1",
			},
			{
				Service: "proftpd",
				Pattern: regexp.MustCompile(`220.*ProFTPD ([^\r\n\s]+)`),
				Version: "$1",
			},
		},
	}

	// SMTP探测
	f.probes["SMTP"] = ProbeConfig{
		Name:  "SMTP",
		Data:  "EHLO fscan\r\n",
		Ports: []int{25, 587, 465},
		Patterns: []PatternMatch{
			{
				Service: "smtp",
				Pattern: regexp.MustCompile(`220[^\r\n]*`),
				Version: "SMTP",
			},
			{
				Service: "postfix",
				Pattern: regexp.MustCompile(`220.*Postfix`),
				Version: "Postfix",
			},
			{
				Service: "sendmail",
				Pattern: regexp.MustCompile(`220.*Sendmail ([^\r\n\s]+)`),
				Version: "$1",
			},
		},
	}

	// MySQL探测
	f.probes["MySQL"] = ProbeConfig{
		Name:  "MySQL",
		Data:  "",
		Ports: []int{3306},
		Patterns: []PatternMatch{
			{
				Service: "mysql",
				Pattern: regexp.MustCompile(`\x00\x00\x00\x0a([0-9\.]+)`),
				Version: "$1",
			},
		},
	}

	// Redis探测
	f.probes["Redis"] = ProbeConfig{
		Name:  "Redis",
		Data:  "INFO\r\n",
		Ports: []int{6379},
		Patterns: []PatternMatch{
			{
				Service: "redis",
				Pattern: regexp.MustCompile(`redis_version:([^\r\n]+)`),
				Version: "$1",
			},
		},
	}

	// PostgreSQL探测
	f.probes["PostgreSQL"] = ProbeConfig{
		Name:  "PostgreSQL",
		Data:  "",
		Ports: []int{5432},
		Patterns: []PatternMatch{
			{
				Service: "postgresql",
				Pattern: regexp.MustCompile(`PostgreSQL`),
				Version: "PostgreSQL",
			},
		},
	}

	// MongoDB探测
	f.probes["MongoDB"] = ProbeConfig{
		Name:  "MongoDB",
		Data:  "",
		Ports: []int{27017},
		Patterns: []PatternMatch{
			{
				Service: "mongodb",
				Pattern: regexp.MustCompile(`MongoDB`),
				Version: "MongoDB",
			},
		},
	}

	global.LOG.Debug("FScan指纹识别器初始化完成", zap.Int("probes", len(f.probes)))
}

// IdentifyService 识别服务 - 基于fscan的指纹识别逻辑
func (f *FScanFingerprint) IdentifyService(host string, port int, banner string) FingerprintResult {
	result := FingerprintResult{
		Service:    "unknown",
		Confidence: 0,
	}

	// 如果有banner，先基于banner识别
	if banner != "" {
		result = f.matchBanner(port, banner)
		if result.Confidence > 0 {
			return result
		}
	}

	// 主动探测
	result = f.activeProbe(host, port)

	return result
}

// matchBanner 基于banner匹配服务
func (f *FScanFingerprint) matchBanner(port int, banner string) FingerprintResult {
	result := FingerprintResult{
		Service:    "unknown",
		Confidence: 0,
	}

	// 遍历所有探测配置
	for _, probe := range f.probes {
		// 检查端口是否匹配
		if !f.portMatches(port, probe.Ports) {
			continue
		}

		// 尝试匹配模式
		for _, pattern := range probe.Patterns {
			if pattern.Pattern.MatchString(banner) {
				result.Service = pattern.Service
				result.Confidence = 80

				// 提取版本信息
				matches := pattern.Pattern.FindStringSubmatch(banner)
				if len(matches) > 1 && pattern.Version != "" {
					version := pattern.Version
					for i, match := range matches[1:] {
						placeholder := "$" + strconv.Itoa(i+1)
						version = strings.ReplaceAll(version, placeholder, match)
					}
					result.Version = version
				}

				global.LOG.Debug("Banner匹配成功",
					zap.String("service", result.Service),
					zap.String("version", result.Version),
					zap.Int("port", port))

				return result
			}
		}
	}

	return result
}

// activeProbe 主动探测服务
func (f *FScanFingerprint) activeProbe(host string, port int) FingerprintResult {
	result := FingerprintResult{
		Service:    "unknown",
		Confidence: 0,
	}

	// 选择合适的探测器
	var selectedProbes []ProbeConfig
	for _, probe := range f.probes {
		if f.portMatches(port, probe.Ports) {
			selectedProbes = append(selectedProbes, probe)
		}
	}

	// 如果没有特定探测器，使用通用探测
	if len(selectedProbes) == 0 {
		selectedProbes = []ProbeConfig{f.probes["HTTP"]} // 默认尝试HTTP
	}

	// 执行探测
	for _, probe := range selectedProbes {
		probeResult := f.executeProbe(host, port, probe)
		if probeResult.Confidence > result.Confidence {
			result = probeResult
		}
	}

	return result
}

// executeProbe 执行单个探测
func (f *FScanFingerprint) executeProbe(host string, port int, probe ProbeConfig) FingerprintResult {
	result := FingerprintResult{
		Service:    "unknown",
		Confidence: 0,
	}

	// 连接目标
	address := net.JoinHostPort(host, strconv.Itoa(port))
	conn, err := net.DialTimeout("tcp", address, 5*time.Second)
	if err != nil {
		return result
	}
	defer conn.Close()

	// 设置超时
	conn.SetDeadline(time.Now().Add(5 * time.Second))

	// 发送探测数据
	if probe.Data != "" {
		data := strings.ReplaceAll(probe.Data, "{host}", host)
		_, err = conn.Write([]byte(data))
		if err != nil {
			return result
		}
	}

	// 读取响应
	scanner := bufio.NewScanner(conn)
	var response strings.Builder

	// 读取多行响应
	for i := 0; i < 10 && scanner.Scan(); i++ {
		response.WriteString(scanner.Text())
		response.WriteString("\n")
	}

	responseText := response.String()
	if responseText == "" {
		return result
	}

	// 匹配模式
	for _, pattern := range probe.Patterns {
		if pattern.Pattern.MatchString(responseText) {
			result.Service = pattern.Service
			result.Confidence = 90

			// 提取版本信息
			matches := pattern.Pattern.FindStringSubmatch(responseText)
			if len(matches) > 1 && pattern.Version != "" {
				version := pattern.Version
				for i, match := range matches[1:] {
					placeholder := "$" + strconv.Itoa(i+1)
					version = strings.ReplaceAll(version, placeholder, match)
				}
				result.Version = version
			}

			global.LOG.Debug("主动探测成功",
				zap.String("probe", probe.Name),
				zap.String("service", result.Service),
				zap.String("version", result.Version),
				zap.Int("port", port))

			return result
		}
	}

	return result
}

// portMatches 检查端口是否匹配
func (f *FScanFingerprint) portMatches(port int, ports []int) bool {
	if len(ports) == 0 {
		return true // 空列表表示匹配所有端口
	}

	for _, p := range ports {
		if p == port {
			return true
		}
	}
	return false
}
