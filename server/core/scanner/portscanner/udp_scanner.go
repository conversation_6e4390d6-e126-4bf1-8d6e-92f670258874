package portscanner

import (
	"fmt"
	"net"
	"server/global"
	"strconv"
	"syscall"
	"time"

	"go.uber.org/zap"
)

// UDPScan 高级UDP扫描
func (s *AdvancedScanner) UDPScan(target ScanTarget) ScanResult {
	result := ScanResult{
		Target:   target,
		ScanTime: time.Now(),
		Status:   "filtered", // UDP默认为过滤状态
	}

	start := time.Now()

	// 执行多种UDP探测技术
	status, banner, err := s.performAdvancedUDPScan(target.IP, target.Port)
	result.ResponseTime = time.Since(start)

	if err != nil {
		result.Error = err
		result.Status = "error"
		return result
	}

	result.Status = status
	result.Banner = banner
	return result
}

// performAdvancedUDPScan 执行高级UDP扫描
func (s *AdvancedScanner) performAdvancedUDPScan(ip net.IP, port int) (string, string, error) {
	// 1. 尝试服务特定的探测
	if status, banner, err := s.udpServiceProbe(ip, port); err == nil && status != "filtered" {
		return status, banner, nil
	}

	// 2. 尝试通用UDP探测
	if status, banner, err := s.udpGenericProbe(ip, port); err == nil && status != "filtered" {
		return status, banner, nil
	}

	// 3. 尝试ICMP响应分析
	if status, err := s.udpICMPProbe(ip, port); err == nil {
		return status, "", nil
	}

	// 默认返回过滤状态
	return "filtered", "", nil
}

// udpServiceProbe 服务特定的UDP探测
func (s *AdvancedScanner) udpServiceProbe(ip net.IP, port int) (string, string, error) {
	// 根据端口号选择特定的探测数据
	var probeData []byte
	var expectedResponse string

	switch port {
	case 53: // DNS
		// 尝试多个DNS查询，优先使用本地和通用域名
		return s.probeDNSService(ip, port)

	case 123: // NTP
		// NTP请求包 - 标准NTP客户端请求
		probeData = []byte{
			0x1b, // LI=0, VN=3, Mode=3 (client)
			0x00, // Stratum
			0x00, // Poll interval
			0x00, // Precision
			// Root delay (4 bytes)
			0x00, 0x00, 0x00, 0x00,
			// Root dispersion (4 bytes)
			0x00, 0x00, 0x00, 0x00,
			// Reference ID (4 bytes)
			0x00, 0x00, 0x00, 0x00,
			// Reference timestamp (8 bytes)
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			// Origin timestamp (8 bytes)
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			// Receive timestamp (8 bytes)
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
			// Transmit timestamp (8 bytes) - 当前时间戳
			0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01,
		}
		expectedResponse = "ntp"

	case 161: // SNMP
		// SNMP GetRequest
		probeData = []byte{
			0x30, 0x26, 0x02, 0x01, 0x00, 0x04, 0x06, 0x70,
			0x75, 0x62, 0x6c, 0x69, 0x63, 0xa0, 0x19, 0x02,
			0x04, 0x00, 0x00, 0x00, 0x00, 0x02, 0x01, 0x00,
			0x02, 0x01, 0x00, 0x30, 0x0b, 0x30, 0x09, 0x06,
			0x05, 0x2b, 0x06, 0x01, 0x02, 0x01, 0x05, 0x00,
		}
		expectedResponse = "snmp"

	case 69: // TFTP
		// TFTP Read Request
		probeData = []byte{
			0x00, 0x01, // Opcode: Read Request
			0x74, 0x65, 0x73, 0x74, 0x00, // Filename: "test"
			0x6f, 0x63, 0x74, 0x65, 0x74, 0x00, // Mode: "octet"
		}
		expectedResponse = "tftp"

	case 1900: // UPnP SSDP
		// UPnP SSDP M-SEARCH请求
		probeData = []byte("M-SEARCH * HTTP/1.1\r\n" +
			"HOST: ***************:1900\r\n" +
			"MAN: \"ssdp:discover\"\r\n" +
			"ST: upnp:rootdevice\r\n" +
			"MX: 3\r\n\r\n")
		expectedResponse = "upnp"

	case 5353: // mDNS
		// mDNS查询包
		probeData = []byte{
			0x00, 0x00, // Transaction ID
			0x01, 0x00, // Flags: standard query
			0x00, 0x01, // Questions: 1
			0x00, 0x00, // Answer RRs: 0
			0x00, 0x00, // Authority RRs: 0
			0x00, 0x00, // Additional RRs: 0
			// Query: _services._dns-sd._udp.local PTR
			0x09, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, // "_services"
			0x07, 0x5f, 0x64, 0x6e, 0x73, 0x2d, 0x73, 0x64, // "_dns-sd"
			0x04, 0x5f, 0x75, 0x64, 0x70, // "_udp"
			0x05, 0x6c, 0x6f, 0x63, 0x61, 0x6c, // "local"
			0x00,       // End of name
			0x00, 0x0c, // Type: PTR
			0x00, 0x01, // Class: IN
		}
		expectedResponse = "mdns"

	case 137: // NetBIOS Name Service
		// NetBIOS名称查询
		probeData = []byte{
			0x12, 0x34, // Transaction ID
			0x01, 0x10, // Flags: query, broadcast
			0x00, 0x01, // Questions: 1
			0x00, 0x00, // Answer RRs: 0
			0x00, 0x00, // Authority RRs: 0
			0x00, 0x00, // Additional RRs: 0
			// Query: *<00><00><00><00><00><00><00><00><00><00><00><00><00><00><00> NB
			0x20, 0x43, 0x4b, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41,
			0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41, 0x41,
			0x00,       // End of name
			0x00, 0x20, // Type: NB
			0x00, 0x01, // Class: IN
		}
		expectedResponse = "netbios"

	default:
		// 通用探测数据 - 使用多种常见的探测字符串
		probeData = []byte("PROBE_TEST")
		expectedResponse = "generic"
	}

	return s.sendUDPProbe(ip, port, probeData, expectedResponse)
}

// udpGenericProbe 通用UDP探测
func (s *AdvancedScanner) udpGenericProbe(ip net.IP, port int) (string, string, error) {
	// 发送多种通用探测数据，优化为更通用的探测
	probes := []struct {
		name string
		data []byte
	}{
		{"empty", []byte("")},                                           // 空包
		{"null", []byte("\x00")},                                        // NULL字节
		{"ping", []byte("ping")},                                        // 简单ping
		{"test", []byte("test")},                                        // 简单文本
		{"hello", []byte("hello")},                                      // 问候
		{"version", []byte("version")},                                  // 版本查询
		{"status", []byte("status")},                                    // 状态查询
		{"info", []byte("info")},                                        // 信息查询
		{"null_block", []byte("\x00\x00\x00\x00")},                      // 4个NULL字节
		{"binary_probe", []byte("\x01\x02\x03\x04")},                    // 二进制探测
		{"http_like", []byte("GET / HTTP/1.0\r\n\r\n")},                 // HTTP类请求
		{"telnet_like", []byte("\xff\xfb\x01\xff\xfb\x03\xff\xfc\x27")}, // Telnet协商
	}

	for _, probe := range probes {
		status, banner, err := s.sendUDPProbe(ip, port, probe.data, "generic")
		if err == nil && status == "open" {
			global.LOG.Debug("通用UDP探测成功",
				zap.String("ip", ip.String()),
				zap.Int("port", port),
				zap.String("probe", probe.name))
			return status, banner, nil
		}

		// 短暂延迟，避免过于激进的探测
		time.Sleep(10 * time.Millisecond)
	}

	return "filtered", "", nil
}

// sendUDPProbe 发送UDP探测包
func (s *AdvancedScanner) sendUDPProbe(ip net.IP, port int, data []byte, probeType string) (string, string, error) {
	address := net.JoinHostPort(ip.String(), strconv.Itoa(port))

	// 创建UDP连接
	conn, err := net.DialTimeout("udp", address, s.timeout)
	if err != nil {
		return "filtered", "", err
	}
	defer conn.Close()

	// 发送探测数据
	_, err = conn.Write(data)
	if err != nil {
		return "filtered", "", err
	}

	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(s.timeout))

	// 尝试读取响应
	buffer := make([]byte, 4096)
	n, err := conn.Read(buffer)

	if err == nil && n > 0 {
		// 收到响应，端口开放
		banner := string(buffer[:n])
		global.LOG.Debug("UDP探测收到响应",
			zap.String("ip", ip.String()),
			zap.Int("port", port),
			zap.String("probeType", probeType),
			zap.Int("responseSize", n))
		return "open", banner, nil
	}

	if netErr, ok := err.(net.Error); ok && netErr.Timeout() {
		// 超时，可能开放但无响应
		return "open|filtered", "", nil
	}

	// 其他错误，可能关闭
	return "filtered", "", err
}

// udpICMPProbe 通过ICMP响应分析UDP端口状态
func (s *AdvancedScanner) udpICMPProbe(ip net.IP, port int) (string, error) {
	// 这需要原始套接字权限来监听ICMP
	if !s.hasRawSocketPermission() {
		return "filtered", fmt.Errorf("需要root权限监听ICMP")
	}

	// 创建ICMP监听套接字
	fd, err := syscall.Socket(syscall.AF_INET, syscall.SOCK_RAW, syscall.IPPROTO_ICMP)
	if err != nil {
		return "filtered", fmt.Errorf("创建ICMP套接字失败: %v", err)
	}
	defer syscall.Close(fd)

	// 发送UDP探测包
	go func() {
		s.sendUDPProbe(ip, port, []byte("ICMP_TEST"), "icmp")
	}()

	// 监听ICMP响应
	buffer := make([]byte, 1500)
	timeout := time.Now().Add(s.timeout)

	for time.Now().Before(timeout) {
		// 设置接收超时
		tv := syscall.Timeval{
			Sec:  0,
			Usec: 100000, // 100ms
		}
		syscall.SetsockoptTimeval(fd, syscall.SOL_SOCKET, syscall.SO_RCVTIMEO, &tv)

		n, _, err := syscall.Recvfrom(fd, buffer, 0)
		if err != nil {
			continue
		}

		if n < 28 { // IP头(20) + ICMP头(8)
			continue
		}

		// 检查是否是目标IP的ICMP响应
		srcIP := net.IPv4(buffer[12], buffer[13], buffer[14], buffer[15])
		if !srcIP.Equal(ip) {
			continue
		}

		// 检查ICMP类型
		icmpType := buffer[20]
		icmpCode := buffer[21]

		// Type 3 Code 3 = Port Unreachable
		if icmpType == 3 && icmpCode == 3 {
			return "closed", nil
		}

		// Type 3 Code 1/2/9/10/13 = 被过滤
		if icmpType == 3 && (icmpCode == 1 || icmpCode == 2 || icmpCode == 9 || icmpCode == 10 || icmpCode == 13) {
			return "filtered", nil
		}
	}

	// 没有收到ICMP响应，可能开放
	return "open|filtered", nil
}

// probeDNSService 专门的DNS服务探测
func (s *AdvancedScanner) probeDNSService(ip net.IP, port int) (string, string, error) {
	// 检测网络环境并获取最优的DNS查询列表
	environment := s.detectNetworkEnvironment()
	dnsQueries := s.getOptimalDNSQueries(environment)

	global.LOG.Debug("DNS探测环境检测",
		zap.String("ip", ip.String()),
		zap.Int("port", port),
		zap.String("environment", environment),
		zap.Int("queries", len(dnsQueries)))

	// 尝试每个DNS查询
	for _, query := range dnsQueries {
		status, banner, err := s.sendUDPProbe(ip, port, query.data, "dns")
		if err == nil && status == "open" {
			global.LOG.Debug("DNS探测成功",
				zap.String("ip", ip.String()),
				zap.Int("port", port),
				zap.String("query", query.name),
				zap.String("description", query.description))

			// 尝试解析DNS响应获取更多信息
			if banner != "" {
				dnsInfo := s.parseDNSResponse([]byte(banner))
				if dnsInfo != "" {
					banner = dnsInfo
				}
			}

			return status, banner, nil
		}

		// 如果这个查询失败，记录并尝试下一个
		global.LOG.Debug("DNS查询失败，尝试下一个",
			zap.String("ip", ip.String()),
			zap.Int("port", port),
			zap.String("query", query.name),
			zap.Error(err))
	}

	// 所有查询都失败，返回过滤状态
	return "filtered", "", fmt.Errorf("所有DNS查询都失败")
}

// parseDNSResponse 解析DNS响应获取服务信息
func (s *AdvancedScanner) parseDNSResponse(response []byte) string {
	if len(response) < 12 {
		return ""
	}

	// 检查DNS响应头
	flags := uint16(response[2])<<8 | uint16(response[3])

	// 检查是否是响应包
	if flags&0x8000 == 0 {
		return ""
	}

	// 检查响应码
	rcode := flags & 0x000F

	switch rcode {
	case 0:
		return "DNS Server (NOERROR)"
	case 1:
		return "DNS Server (FORMERR)"
	case 2:
		return "DNS Server (SERVFAIL)"
	case 3:
		return "DNS Server (NXDOMAIN)"
	case 4:
		return "DNS Server (NOTIMP)"
	case 5:
		return "DNS Server (REFUSED)"
	default:
		return fmt.Sprintf("DNS Server (RCODE=%d)", rcode)
	}
}

// detectNetworkEnvironment 检测网络环境（国内/国外）
func (s *AdvancedScanner) detectNetworkEnvironment() string {
	// 简单的网络环境检测
	// 尝试解析一些国内外常见的域名来判断网络环境

	// 检测国内网络环境的指标
	domesticIndicators := []string{
		"baidu.com",
		"qq.com",
		"taobao.com",
	}

	// 检测国外网络环境的指标
	internationalIndicators := []string{
		"google.com",
		"facebook.com",
		"twitter.com",
	}

	domesticSuccess := 0
	internationalSuccess := 0

	// 快速DNS解析测试
	for _, domain := range domesticIndicators {
		if _, err := net.LookupHost(domain); err == nil {
			domesticSuccess++
		}
	}

	for _, domain := range internationalIndicators {
		if _, err := net.LookupHost(domain); err == nil {
			internationalSuccess++
		}
	}

	// 根据成功率判断网络环境
	if domesticSuccess > internationalSuccess {
		return "domestic" // 国内环境
	} else if internationalSuccess > domesticSuccess {
		return "international" // 国际环境
	} else {
		return "unknown" // 未知环境
	}
}

// getOptimalDNSQueries 根据网络环境获取最优的DNS查询列表
func (s *AdvancedScanner) getOptimalDNSQueries(environment string) []struct {
	name        string
	description string
	data        []byte
} {
	baseQueries := []struct {
		name        string
		description string
		data        []byte
	}{
		{
			name:        "localhost",
			description: "本地主机查询",
			data: []byte{
				0x12, 0x34, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x68, 0x6f, 0x73, 0x74,
				0x00, 0x00, 0x01, 0x00, 0x01,
			},
		},
	}

	// 根据环境添加特定的查询
	switch environment {
	case "domestic":
		// 国内环境，优先使用国内域名
		baseQueries = append(baseQueries, struct {
			name        string
			description string
			data        []byte
		}{
			name:        "baidu.com",
			description: "国内通用域名",
			data: []byte{
				0x12, 0x35, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x05, 0x62, 0x61, 0x69, 0x64, 0x75, 0x03, 0x63, 0x6f, 0x6d,
				0x00, 0x00, 0x01, 0x00, 0x01,
			},
		})

	case "international":
		// 国际环境，可以使用国际域名
		baseQueries = append(baseQueries, struct {
			name        string
			description string
			data        []byte
		}{
			name:        "example.com",
			description: "RFC标准测试域名",
			data: []byte{
				0x12, 0x36, 0x01, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x07, 0x65, 0x78, 0x61, 0x6d, 0x70, 0x6c, 0x65, 0x03, 0x63, 0x6f, 0x6d,
				0x00, 0x00, 0x01, 0x00, 0x01,
			},
		})

	default:
		// 未知环境，使用保守的查询
		baseQueries = append(baseQueries, struct {
			name        string
			description string
			data        []byte
		}{
			name:        "version.bind",
			description: "BIND版本查询",
			data: []byte{
				0x12, 0x37, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
				0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x04, 0x62, 0x69, 0x6e, 0x64,
				0x00, 0x00, 0x10, 0x00, 0x03,
			},
		})
	}

	return baseQueries
}
