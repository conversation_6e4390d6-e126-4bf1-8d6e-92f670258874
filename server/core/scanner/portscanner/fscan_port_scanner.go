package portscanner

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"
	"sync"
	"time"

	"server/global"

	"go.uber.org/zap"
)

// FScanPortScanner 基于fscan的高性能端口扫描器
type FScanPortScanner struct {
	ctx     context.Context
	cancel  context.CancelFunc
	timeout time.Duration
	workers int
}

// FScanResult 扫描结果
type FScanResult struct {
	IP       string
	Port     int
	Protocol string
	State    string
	Service  string
	Banner   string
	Error    error
}

// NewFScanPortScanner 创建基于fscan的端口扫描器
func NewFScanPortScanner(workers int, timeout time.Duration) *FScanPortScanner {
	ctx, cancel := context.WithCancel(context.Background())

	if workers <= 0 {
		workers = 1000 // fscan默认使用高并发
	}

	if timeout <= 0 {
		timeout = 3 * time.Second
	}

	return &FScanPortScanner{
		ctx:     ctx,
		cancel:  cancel,
		timeout: timeout,
		workers: workers,
	}
}

// Start 启动扫描器
func (f *FScanPortScanner) Start() {
	global.LOG.Debug("FScan端口扫描器启动",
		zap.Int("workers", f.workers),
		zap.Duration("timeout", f.timeout))
}

// Stop 停止扫描器
func (f *FScanPortScanner) Stop() {
	if f.cancel != nil {
		f.cancel()
	}
	global.LOG.Debug("FScan端口扫描器停止")
}

// ScanPorts 扫描端口 - 基于fscan的端口扫描逻辑
func (f *FScanPortScanner) ScanPorts(targets []string, ports []int, progressCallback func(float64), resultCallback func(FScanResult)) error {
	if len(targets) == 0 || len(ports) == 0 {
		return fmt.Errorf("目标或端口列表为空")
	}

	global.LOG.Info("开始FScan端口扫描",
		zap.Int("targets", len(targets)),
		zap.Int("ports", len(ports)),
		zap.Int("workers", f.workers))

	// 创建任务通道
	taskChan := make(chan ScanTask, f.workers*2)
	resultChan := make(chan FScanResult, f.workers*2)

	// 启动工作协程
	var wg sync.WaitGroup
	for i := 0; i < f.workers; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			f.worker(taskChan, resultChan)
		}()
	}

	// 启动结果处理协程
	var resultWg sync.WaitGroup
	resultWg.Add(1)
	go func() {
		defer resultWg.Done()
		f.handleResults(resultChan, resultCallback)
	}()

	// 启动进度监控
	totalTasks := int64(len(targets) * len(ports))
	var completedTasks int64
	if progressCallback != nil {
		go f.monitorProgress(resultChan, totalTasks, &completedTasks, progressCallback)
	}

	// 生成扫描任务
	go func() {
		defer close(taskChan)
		for _, target := range targets {
			for _, port := range ports {
				select {
				case <-f.ctx.Done():
					return
				case taskChan <- ScanTask{
					Target: target,
					Port:   port,
				}:
				}
			}
		}
	}()

	// 等待所有工作完成
	wg.Wait()
	close(resultChan)
	resultWg.Wait()

	global.LOG.Info("FScan端口扫描完成")
	return nil
}

// ScanTask 扫描任务
type ScanTask struct {
	Target string
	Port   int
}

// worker 工作协程 - 基于fscan的连接扫描
func (f *FScanPortScanner) worker(taskChan <-chan ScanTask, resultChan chan<- FScanResult) {
	for {
		select {
		case <-f.ctx.Done():
			return
		case task, ok := <-taskChan:
			if !ok {
				return
			}

			result := f.scanPort(task.Target, task.Port)

			select {
			case <-f.ctx.Done():
				return
			case resultChan <- result:
			}
		}
	}
}

// scanPort 扫描单个端口 - 基于fscan的Connect扫描
func (f *FScanPortScanner) scanPort(target string, port int) FScanResult {
	result := FScanResult{
		IP:       target,
		Port:     port,
		Protocol: "tcp",
		State:    "closed",
	}

	// 构建地址
	address := net.JoinHostPort(target, strconv.Itoa(port))

	// 尝试TCP连接
	conn, err := net.DialTimeout("tcp", address, f.timeout)
	if err != nil {
		result.Error = err
		return result
	}
	defer conn.Close()

	// 连接成功，端口开放
	result.State = "open"

	// 尝试获取banner - 基于fscan的banner抓取
	banner := f.grabBanner(conn, port)
	if banner != "" {
		result.Banner = banner
		result.Service = f.identifyService(port, banner)
	} else {
		result.Service = f.getDefaultService(port)
	}

	global.LOG.Debug("端口扫描结果",
		zap.String("target", target),
		zap.Int("port", port),
		zap.String("state", result.State),
		zap.String("service", result.Service))

	return result
}

// grabBanner 抓取banner - 基于fscan的banner抓取逻辑
func (f *FScanPortScanner) grabBanner(conn net.Conn, port int) string {
	// 设置读取超时
	conn.SetReadDeadline(time.Now().Add(3 * time.Second))

	// 根据端口发送特定的探测数据
	probeData := f.getProbeData(port)
	if probeData != "" {
		conn.Write([]byte(probeData))
	}

	// 读取响应
	buffer := make([]byte, 1024)
	n, err := conn.Read(buffer)
	if err != nil {
		return ""
	}

	banner := string(buffer[:n])
	// 清理banner，移除不可打印字符
	banner = strings.Map(func(r rune) rune {
		if r >= 32 && r <= 126 {
			return r
		}
		return -1
	}, banner)

	return strings.TrimSpace(banner)
}

// getProbeData 获取端口探测数据 - 基于fscan的探测逻辑
func (f *FScanPortScanner) getProbeData(port int) string {
	switch port {
	case 21: // FTP
		return ""
	case 22: // SSH
		return ""
	case 23: // Telnet
		return ""
	case 25: // SMTP
		return "EHLO fscan\r\n"
	case 53: // DNS
		return ""
	case 80, 8080, 8081, 8000, 8888: // HTTP
		return "GET / HTTP/1.1\r\nHost: " + "fscan" + "\r\n\r\n"
	case 110: // POP3
		return ""
	case 143: // IMAP
		return ""
	case 443, 8443: // HTTPS
		return "GET / HTTP/1.1\r\nHost: " + "fscan" + "\r\n\r\n"
	case 993: // IMAPS
		return ""
	case 995: // POP3S
		return ""
	case 1433: // MSSQL
		return ""
	case 3306: // MySQL
		return ""
	case 3389: // RDP
		return ""
	case 5432: // PostgreSQL
		return ""
	case 6379: // Redis
		return "INFO\r\n"
	case 27017: // MongoDB
		return ""
	default:
		return ""
	}
}

// identifyService 识别服务 - 基于fscan的服务识别
func (f *FScanPortScanner) identifyService(port int, banner string) string {
	banner = strings.ToLower(banner)

	// 基于banner识别
	if strings.Contains(banner, "ssh") {
		return "ssh"
	}
	if strings.Contains(banner, "ftp") {
		return "ftp"
	}
	if strings.Contains(banner, "http") {
		return "http"
	}
	if strings.Contains(banner, "mysql") {
		return "mysql"
	}
	if strings.Contains(banner, "redis") {
		return "redis"
	}
	if strings.Contains(banner, "mongodb") {
		return "mongodb"
	}
	if strings.Contains(banner, "postgresql") {
		return "postgresql"
	}

	// 基于端口识别
	return f.getDefaultService(port)
}

// getDefaultService 获取默认服务名 - 基于fscan的端口映射
func (f *FScanPortScanner) getDefaultService(port int) string {
	serviceMap := map[int]string{
		21:    "ftp",
		22:    "ssh",
		23:    "telnet",
		25:    "smtp",
		53:    "dns",
		80:    "http",
		110:   "pop3",
		143:   "imap",
		443:   "https",
		993:   "imaps",
		995:   "pop3s",
		1433:  "mssql",
		3306:  "mysql",
		3389:  "rdp",
		5432:  "postgresql",
		6379:  "redis",
		8080:  "http-proxy",
		8443:  "https-alt",
		27017: "mongodb",
	}

	if service, exists := serviceMap[port]; exists {
		return service
	}
	return "unknown"
}

// handleResults 处理扫描结果
func (f *FScanPortScanner) handleResults(resultChan <-chan FScanResult, callback func(FScanResult)) {
	for result := range resultChan {
		if callback != nil {
			callback(result)
		}
	}
}

// monitorProgress 监控进度
func (f *FScanPortScanner) monitorProgress(resultChan <-chan FScanResult, total int64, completed *int64, callback func(float64)) {
	for range resultChan {
		*completed++
		progress := float64(*completed) / float64(total) * 100
		callback(progress)
	}
}
