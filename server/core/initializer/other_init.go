package initializer

import (
	"os"
	"path/filepath"
	"server/global"
	"server/model/sys"
	"time"

	"github.com/songzhibin97/gkit/cache/local_cache"
)

func OtherInit() {
	dr, err := sys.ParseDuration(global.CONFIG.JWT.ExpiresTime)
	if err != nil {
		panic(err)
	}
	_, err = sys.ParseDuration(global.CONFIG.JWT.BufferTime)
	if err != nil {
		panic(err)
	}

	global.BlackCache = local_cache.NewCache(
		local_cache.SetDefaultExpire(dr),
	)

	// 初始化响应缓存，用于存储客户端操作响应，设置5分钟过期时间
	global.ResponseCache = local_cache.NewCache(
		local_cache.SetDefaultExpire(time.Minute * 5),
	)

	// 初始化客户端二进制文件存储目录
	initClientBinDir()

	// 注意：指纹识别引擎已由FScan系统替代
}

// 初始化客户端二进制文件存储目录
func initClientBinDir() {
	// 如果配置中指定了目录，则使用配置的目录
	if global.CONFIG.Server.ClientBinDir != "" {
		global.CLIENT_BIN_DIR = global.CONFIG.Server.ClientBinDir
	} else {
		// 否则使用默认目录（main.go同级目录下的clientbin目录）
		execPath, err := os.Executable()
		if err != nil {
			global.LOG.Error("获取可执行文件路径失败，使用当前目录作为客户端二进制存储目录")
			global.CLIENT_BIN_DIR = "./clientbin"
		} else {
			execDir := filepath.Dir(execPath)
			global.CLIENT_BIN_DIR = filepath.Join(execDir, "clientbin")
		}
	}

	// 确保目录存在
	if err := os.MkdirAll(global.CLIENT_BIN_DIR, 0755); err != nil {
		global.LOG.Error("创建客户端二进制存储目录失败: " + err.Error())
	}

	global.LOG.Info("客户端二进制存储目录: " + global.CLIENT_BIN_DIR)
}

// 注意：initFingerprintEngine 方法已移除，指纹识别功能由FScan系统处理
