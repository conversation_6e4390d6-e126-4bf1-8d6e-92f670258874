package scan

import (
	"fmt"
	scanner "server/core/scanner/portscanner"
	"server/global"
	"server/model/request/scan"
	"server/model/response"
	"server/service"
	"server/utils"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type PortScanApi struct{}

// CreateTask 创建扫描任务
// @Tags 端口扫描
// @Summary 创建端口扫描任务
// @Description 创建一个新的端口扫描任务
// @Accept json
// @Produce json
// @Param data body scan.CreateScanTaskRequest true "创建任务请求"
// @Success 200 {object} response.Response{data=scanModel.PortScanTask} "创建成功"
// @Router /api/scan/port/tasks [post]
func (a *PortScanApi) CreateTask(c *gin.Context) {
	var req scan.CreateScanTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 获取当前用户ID
	userID := utils.GetUserID(c)

	// 创建任务
	task, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.CreateScanTask(req, userID)
	if err != nil {
		global.LOG.Error("创建端口扫描任务失败", zap.Error(err))
		response.ErrorWithMessage("创建任务失败: "+err.Error(), c)
		return
	}

	global.LOG.Info("创建端口扫描任务成功",
		zap.Uint("taskID", task.ID),
		zap.String("name", task.Name),
		zap.Uint("userID", userID))

	response.OkWithDetailed(task, "创建任务成功", c)
}

// StartTask 启动扫描任务
// @Tags 端口扫描
// @Summary 启动扫描任务
// @Description 启动指定的端口扫描任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "启动成功"
// @Router /api/scan/port/tasks/{id}/start [post]
func (a *PortScanApi) StartTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	err = service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.StartScan(taskID)
	if err != nil {
		global.LOG.Error("启动端口扫描任务失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		response.ErrorWithMessage("启动任务失败: "+err.Error(), c)
		return
	}

	global.LOG.Info("启动端口扫描任务成功", zap.Uint64("taskID", taskID))
	response.OkWithMessage("启动任务成功", c)
}

// StopTask 停止扫描任务
// @Tags 端口扫描
// @Summary 停止扫描任务
// @Description 停止指定的端口扫描任务
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "停止成功"
// @Router /api/scan/port/tasks/{id}/stop [post]
func (a *PortScanApi) StopTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	err = service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.StopScan(taskID)
	if err != nil {
		global.LOG.Error("停止端口扫描任务失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		response.ErrorWithMessage("停止任务失败: "+err.Error(), c)
		return
	}

	global.LOG.Info("停止端口扫描任务成功", zap.Uint64("taskID", taskID))
	response.OkWithMessage("停止任务成功", c)
}

// GetTask 获取扫描任务详情
// @Tags 端口扫描
// @Summary 获取扫描任务详情
// @Description 获取指定扫描任务的详细信息
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response{data=scanModel.PortScanTask} "获取成功"
// @Router /api/scan/port/tasks/{id} [get]
func (a *PortScanApi) GetTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	task, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanTask(taskID)
	if err != nil {
		global.LOG.Error("获取端口扫描任务失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		response.ErrorWithMessage("获取任务失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(task, "获取任务成功", c)
}

// GetTaskList 获取扫描任务列表
// @Tags 端口扫描
// @Summary 获取扫描任务列表
// @Description 获取端口扫描任务列表，支持分页和过滤
// @Accept json
// @Produce json
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param status query string false "任务状态"
// @Param scan_type query string false "扫描类型"
// @Param keyword query string false "关键词"
// @Success 200 {object} response.Response{data=gin.H} "获取成功"
// @Router /api/scan/port/tasks [get]
func (a *PortScanApi) GetTaskList(c *gin.Context) {
	var req scan.GetScanTaskListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	tasks, total, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanTaskList(req)
	if err != nil {
		global.LOG.Error("获取端口扫描任务列表失败", zap.Error(err))
		response.ErrorWithMessage("获取任务列表失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(gin.H{
		"list":      tasks,
		"total":     total,
		"page":      req.Page,
		"page_size": req.PageSize,
		"has_more":  int64(req.Page*req.PageSize) < total,
	}, "获取任务列表成功", c)
}

// GetResults 获取扫描结果
// @Tags 端口扫描
// @Summary 获取扫描结果
// @Description 获取指定任务的扫描结果，支持分页和过滤
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Param target query string false "目标IP"
// @Param status query string false "端口状态"
// @Param protocol query string false "协议类型"
// @Param port query int false "端口号"
// @Success 200 {object} response.Response{data=scanModel.ScanResultsResponse} "获取成功"
// @Router /api/scan/port/tasks/{id}/results [get]
func (a *PortScanApi) GetResults(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	var req scan.GetScanResultsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.ErrorWithMessage("参数错误: "+err.Error(), c)
		return
	}

	results, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanResults(taskID, req)
	if err != nil {
		global.LOG.Error("获取端口扫描结果失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		response.ErrorWithMessage("获取扫描结果失败: "+err.Error(), c)
		return
	}

	response.OkWithDetailed(results, "获取扫描结果成功", c)
}

// GetProgress 获取扫描进度 (SSE)
// @Tags 端口扫描
// @Summary 获取扫描进度
// @Description 通过SSE实时获取扫描进度信息
// @Accept json
// @Produce text/event-stream
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "SSE流"
// @Router /api/scan/port/tasks/{id}/progress [get]
func (a *PortScanApi) GetProgress(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	// 设置SSE头
	c.Header("Content-Type", "text/event-stream")
	c.Header("Cache-Control", "no-cache")
	c.Header("Connection", "keep-alive")
	c.Header("Access-Control-Allow-Origin", "*")

	// 创建一个ticker，定期发送进度信息
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()

	// 发送初始进度
	a.sendProgress(c, taskID)

	for {
		select {
		case <-c.Request.Context().Done():
			return
		case <-ticker.C:
			// 获取任务状态
			task, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanTask(taskID)
			if err != nil {
				c.SSEvent("error", gin.H{"message": "获取任务失败: " + err.Error()})
				c.Writer.Flush()
				return
			}

			// 发送进度信息
			a.sendProgress(c, taskID)

			// 如果任务已完成，结束SSE
			if task.IsCompleted() {
				c.SSEvent("complete", gin.H{"message": "扫描已完成"})
				c.Writer.Flush()
				return
			}
		}
	}
}

// sendProgress 发送进度信息
func (a *PortScanApi) sendProgress(c *gin.Context, taskID uint64) {
	// 获取任务信息
	task, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanTask(taskID)
	if err != nil {
		c.SSEvent("error", gin.H{"message": "获取任务失败: " + err.Error()})
		c.Writer.Flush()
		return
	}

	// 获取统计信息
	statistics, err := service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.GetScanStatistics(taskID)
	if err != nil {
		global.LOG.Error("获取扫描统计失败", zap.Error(err))
	}

	// 计算预估剩余时间
	var estimatedTime int64
	if statistics != nil && statistics.ScanProgress > 0 && statistics.ScanProgress < 100 {
		if statistics.ElapsedTime > 0 {
			totalEstimated := float64(statistics.ElapsedTime) / (statistics.ScanProgress / 100)
			estimatedTime = int64(totalEstimated) - statistics.ElapsedTime
		}
	}

	progressData := gin.H{
		"task_id":        taskID,
		"status":         task.Status,
		"progress":       task.Progress,
		"elapsed_time":   0,
		"estimated_time": estimatedTime,
		"message":        fmt.Sprintf("扫描进度: %.1f%%", task.Progress),
	}

	if statistics != nil {
		progressData["statistics"] = statistics
		progressData["elapsed_time"] = statistics.ElapsedTime
	}

	if task.StartedAt != nil {
		progressData["current_target"] = "正在扫描..."
	}

	c.SSEvent("progress", progressData)
	c.Writer.Flush()
}

// DeleteTask 删除扫描任务
// @Tags 端口扫描
// @Summary 删除扫描任务
// @Description 删除指定的端口扫描任务及其结果
// @Accept json
// @Produce json
// @Param id path uint64 true "任务ID"
// @Success 200 {object} response.Response "删除成功"
// @Router /api/scan/port/tasks/{id} [delete]
func (a *PortScanApi) DeleteTask(c *gin.Context) {
	taskIDStr := c.Param("id")
	taskID, err := strconv.ParseUint(taskIDStr, 10, 64)
	if err != nil {
		response.ErrorWithMessage("任务ID参数错误", c)
		return
	}

	err = service.ServiceGroupManagerAPP.ScanServiceGroup.PortScanService.DeleteScanTask(taskID)
	if err != nil {
		global.LOG.Error("删除端口扫描任务失败",
			zap.Uint64("taskID", taskID),
			zap.Error(err))
		response.ErrorWithMessage("删除任务失败: "+err.Error(), c)
		return
	}

	global.LOG.Info("删除端口扫描任务成功", zap.Uint64("taskID", taskID))
	response.OkWithMessage("删除任务成功", c)
}

// GetCapabilities 获取扫描器能力
// @Tags 端口扫描
// @Summary 获取扫描器能力
// @Description 检测服务器支持的扫描类型和能力
// @Accept json
// @Produce json
// @Success 200 {object} response.Response "获取成功"
// @Router /api/scan/port/capabilities [get]
func (a *PortScanApi) GetCapabilities(c *gin.Context) {
	// 创建临时高级扫描器来检测能力
	advancedScanner := scanner.NewAdvancedScanner(time.Second, 1)
	capabilities := advancedScanner.GetScannerCapabilities()

	response.OkWithData(gin.H{
		"capabilities": capabilities,
		"message":      "扫描器能力检测完成",
		"scan_types": map[string]string{
			"tcp":     "TCP连接扫描",
			"syn":     "TCP SYN扫描（需要权限）",
			"fin":     "TCP FIN扫描（需要权限）",
			"xmas":    "TCP Xmas扫描（需要权限）",
			"null":    "TCP Null扫描（需要权限）",
			"ack":     "TCP ACK扫描（需要权限）",
			"window":  "TCP Window扫描（需要权限）",
			"stealth": "隐蔽扫描",
			"udp":     "UDP扫描",
			"both":    "TCP+UDP扫描",
		},
	}, c)
}
