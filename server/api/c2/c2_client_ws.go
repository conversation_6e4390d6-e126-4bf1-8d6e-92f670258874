package c2

import (
	"encoding/json"
	"fmt"
	"net/http"
	"server/core/manager/cache"
	"server/core/manager/dbpool"
	"server/core/manager/shutdown"
	"server/global"
	"server/model/sys"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

//本文件用于前端和后端的websocket通信，起到对客户端进行控制的作用

// WebSocket连接升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		// 允许所有来源的WebSocket连接
		return true
	},
}

// WebSocket写入消息类型
type WSWriteMessage struct {
	Type   int         // websocket.TextMessage, websocket.BinaryMessage, websocket.PingMessage等
	Data   interface{} // 数据内容，可以是[]byte或任何可JSON序列化的对象
	IsJSON bool        // 是否使用WriteJSON
}

// WebSocket连接包装器
type SafeWebSocketConn struct {
	conn       *websocket.Conn
	writeQueue chan WSWriteMessage
	done       chan struct{}
	clientID   uint
}

// 创建安全的WebSocket连接包装器
func NewSafeWebSocketConn(conn *websocket.Conn, clientID uint) *SafeWebSocketConn {
	safeConn := &SafeWebSocketConn{
		conn:       conn,
		writeQueue: make(chan WSWriteMessage, 100), // 缓冲队列
		done:       make(chan struct{}),
		clientID:   clientID,
	}

	// 启动写入goroutine
	go safeConn.writeLoop()

	return safeConn
}

// 写入循环，确保所有写入操作都在单一goroutine中执行
func (s *SafeWebSocketConn) writeLoop() {
	defer func() {
		if r := recover(); r != nil {
			global.LOG.Error("WebSocket写入goroutine panic",
				zap.Uint("clientID", s.clientID),
				zap.Any("panic", r))
		}
	}()

	for {
		select {
		case msg := <-s.writeQueue:
			var err error
			if msg.IsJSON {
				err = s.conn.WriteJSON(msg.Data)
			} else {
				if data, ok := msg.Data.([]byte); ok {
					err = s.conn.WriteMessage(msg.Type, data)
				} else {
					global.LOG.Error("无效的写入数据类型",
						zap.Uint("clientID", s.clientID),
						zap.Any("data", msg.Data))
					continue
				}
			}

			if err != nil {
				global.LOG.Error("WebSocket写入失败",
					zap.Uint("clientID", s.clientID),
					zap.Error(err))
				// 写入失败，关闭连接
				s.Close()
				return
			}

		case <-s.done:
			return
		}
	}
}

// 安全的JSON写入
func (s *SafeWebSocketConn) WriteJSON(data interface{}) error {
	select {
	case s.writeQueue <- WSWriteMessage{IsJSON: true, Data: data}:
		return nil
	case <-s.done:
		return fmt.Errorf("连接已关闭")
	default:
		return fmt.Errorf("写入队列已满")
	}
}

// 安全的消息写入
func (s *SafeWebSocketConn) WriteMessage(messageType int, data []byte) error {
	select {
	case s.writeQueue <- WSWriteMessage{Type: messageType, Data: data, IsJSON: false}:
		return nil
	case <-s.done:
		return fmt.Errorf("连接已关闭")
	default:
		return fmt.Errorf("写入队列已满")
	}
}

// 关闭连接
func (s *SafeWebSocketConn) Close() error {
	select {
	case <-s.done:
		return nil // 已经关闭
	default:
		close(s.done)
		close(s.writeQueue)
		return s.conn.Close()
	}
}

// 获取原始连接（用于读取操作）
func (s *SafeWebSocketConn) GetRawConn() *websocket.Conn {
	return s.conn
}

// 客户端WebSocket连接管理
type ClientWebSocketManager struct {
	connections  map[uint][]*SafeWebSocketConn // 客户端ID -> 安全WebSocket连接列表
	MessageStack map[uint][]interface{}
	mutex        sync.RWMutex
}

// 全局WebSocket连接管理器
var clientWSManager = ClientWebSocketManager{
	connections:  make(map[uint][]*SafeWebSocketConn),
	MessageStack: make(map[uint][]interface{}),
}

// 注册WebSocket连接
func (m *ClientWebSocketManager) RegisterConnection(clientID uint, conn *websocket.Conn) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	// 创建安全的WebSocket连接包装器
	safeConn := NewSafeWebSocketConn(conn, clientID)

	// 添加到连接列表
	m.connections[clientID] = append(m.connections[clientID], safeConn)

	if len(m.MessageStack[clientID]) != 0 {
		for _, msg := range m.MessageStack[clientID] {
			mes := msg.(map[string]string)
			global.LOG.Info("发送积累的消息: " + mes["output"])
			err := safeConn.WriteJSON(msg)
			if err != nil {
				global.LOG.Error(fmt.Sprintf("发送WebSocket消息失败: %s", err.Error()))
			}
		}
	}
	delete(m.MessageStack, clientID)
	global.LOG.Info(fmt.Sprintf("注册WebSocket连接，客户端ID: %d", clientID))
}

// 移除WebSocket连接
func (m *ClientWebSocketManager) RemoveConnection(clientID uint, safeConn *SafeWebSocketConn) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	conns, exists := m.connections[clientID]
	if !exists {
		return
	}

	// 查找并移除连接
	for i, c := range conns {
		if c == safeConn {
			// 关闭安全连接
			c.Close()
			m.connections[clientID] = append(conns[:i], conns[i+1:]...)
			break
		}
	}

	// 如果没有连接了，删除客户端条目
	if len(m.connections[clientID]) == 0 {
		delete(m.connections, clientID)
	}

	global.LOG.Info(fmt.Sprintf("移除WebSocket连接，客户端ID: %d", clientID))
}

// 处理WebSocket连接
func (c *ClientApi) HandleWebSocket(ctx *gin.Context) {
	// 验证token
	token := ctx.Query("token")
	if token == "" {
		ctx.String(http.StatusUnauthorized, "未提供认证信息")
		return
	}

	// 验证token有效性
	j := sys.NewJWT()
	_, err := j.ParseToken(token)
	if err != nil {
		global.LOG.Error("WebSocket连接认证失败", zap.Error(err))
		ctx.String(http.StatusUnauthorized, "认证失败")
		return
	}

	// 获取客户端ID
	idStr := ctx.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		ctx.String(http.StatusBadRequest, "无效的客户端ID")
		return
	}

	// 检查客户端是否存在
	client, err := clientService.GetClient(uint(id))
	if err != nil {
		global.LOG.Error("获取客户端失败", zap.Error(err))
		ctx.String(http.StatusNotFound, "客户端不存在")
		return
	}

	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, nil)
	if err != nil {
		global.LOG.Error("升级WebSocket连接失败", zap.Error(err))
		return
	}

	// 注册WebSocket连接
	clientWSManager.RegisterConnection(client.ID, conn)

	// 获取注册的安全连接
	clientWSManager.mutex.RLock()
	var safeConn *SafeWebSocketConn
	if conns, exists := clientWSManager.connections[client.ID]; exists && len(conns) > 0 {
		safeConn = conns[len(conns)-1] // 获取最新注册的连接
	}
	clientWSManager.mutex.RUnlock()

	if safeConn == nil {
		global.LOG.Error("无法获取安全WebSocket连接")
		conn.Close()
		return
	}

	// 启动心跳检测
	go handleHeartbeat(client.ID, safeConn)

	// 处理WebSocket消息
	go handleMessages(client.ID, safeConn)
}

// 处理心跳检测
func handleHeartbeat(clientID uint, safeConn *SafeWebSocketConn) {
	ticker := time.NewTicker(30 * time.Second)
	defer func() {
		ticker.Stop()
		safeConn.Close()
		clientWSManager.RemoveConnection(clientID, safeConn)
	}()

	for {
		select {
		case <-ticker.C:
			// 发送ping消息
			if err := safeConn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				global.LOG.Error(fmt.Sprintf("发送心跳消息失败: %s", err.Error()))
				return
			}
		}
	}
}

// 处理WebSocket消息
func handleMessages(clientID uint, safeConn *SafeWebSocketConn) {
	// 🚀 创建done channel来控制生命周期
	done := make(chan struct{})

	// 🚀 注册goroutine到关闭管理器
	shutdown.RegisterGoroutine()

	// 获取原始连接用于读取操作
	conn := safeConn.GetRawConn()

	defer func() {
		shutdown.UnregisterGoroutine()
		close(done) // 确保所有goroutine退出
		safeConn.Close()
		clientWSManager.RemoveConnection(clientID, safeConn)
		// 清理该客户端的命令输出缓存
		cache.ResponseMgr.ClearCommandOutputs(clientID)
	}()

	// 设置消息处理函数
	conn.SetPongHandler(func(string) error {
		// 收到pong消息，更新最后活动时间
		conn.SetReadDeadline(time.Now().Add(60 * time.Second))
		return nil
	})

	// 🚀 异步读取消息循环
	go func() {
		defer func() {
			// 发送完成信号
			select {
			case done <- struct{}{}:
			default:
			}
		}()

		for {
			conn.SetReadDeadline(time.Now().Add(60 * time.Second))

			msgType, message, err := conn.ReadMessage()
			if err != nil {
				if websocket.IsUnexpectedCloseError(err, websocket.CloseGoingAway, websocket.CloseAbnormalClosure) {
					global.LOG.Error(fmt.Sprintf("WebSocket连接异常关闭: %s", err.Error()))
				}
				return // 退出goroutine
			}

			// 🚀 异步处理消息，避免阻塞读取循环
			go processMessage(clientID, msgType, message)
		}
	}()

	// 🚀 新增：异步读取命令输出并发送到前端
	go func() {
		ticker := time.NewTicker(100 * time.Millisecond) // 每100ms检查一次
		defer ticker.Stop()

		for {
			select {
			case <-done:
				return // 连接关闭，退出
			case <-shutdown.GetShutdownChannel():
				global.LOG.Info("收到服务器关闭信号，停止WebSocket命令输出推送", zap.Uint("clientID", clientID))
				return // 服务器关闭，退出
			case <-ticker.C:
				// 从ResponseManager获取命令输出
				if outputs, found := cache.ResponseMgr.PopCommandOutputs(clientID); found {
					// 发送所有输出到前端
					for _, output := range outputs {
						message := map[string]interface{}{
							"code":    "200",
							"type":    output.Type,
							"content": output.Output,
						}

						// 解析终端ID（如果是多终端格式）
						if strings.HasPrefix(output.Type, "command_output_terminal_") {
							terminalIDStr := strings.TrimPrefix(output.Type, "command_output_terminal_")
							if terminalID, err := strconv.ParseUint(terminalIDStr, 10, 32); err == nil {
								message["terminal_id"] = uint32(terminalID)
								message["type"] = "command_output" // 统一类型
							}
						}

						if err := safeConn.WriteJSON(message); err != nil {
							global.LOG.Error("发送命令输出失败",
								zap.Uint("clientID", clientID),
								zap.Error(err))
							// 连接出错，退出
							select {
							case done <- struct{}{}:
							default:
							}
							return
						}
					}

					global.LOG.Debug("发送命令输出到前端",
						zap.Uint("clientID", clientID),
						zap.Int("outputCount", len(outputs)))
				}
			}
		}
	}()

	// 🚀 等待连接关闭
	<-done
}

// 🚀 新增：异步处理单个消息
func processMessage(clientID uint, msgType int, message []byte) {
	// 处理接收到的消息
	if msgType == websocket.TextMessage || msgType == websocket.BinaryMessage {
		// 处理resize消息
		messageStr := string(message)
		if strings.Contains(messageStr, "action") && strings.Contains(messageStr, "resize") {
			//{"action":"resize","terminal_id":0,"cols":185,"rows":33}
			type ResizeMessage struct {
				Action     string `json:"action"`
				TerminalID uint32 `json:"terminal_id"`
				Cols       uint16 `json:"cols"`
				Rows       uint16 `json:"rows"`
			}
			resizeMessage := &ResizeMessage{}
			if err := json.Unmarshal(message, &resizeMessage); err != nil {
				global.LOG.Error("解析Resize消息失败", zap.Error(err))
				return
			}

			// 检查客户端的监听器类型，pipe监听器不需要处理resize
			var client sys.Client
			if err := dbpool.ExecuteDBOperationAsyncAndWait("resize_client_query", func(db *gorm.DB) error {
				return db.Where("id = ?", clientID).First(&client).Error
			}); err != nil {
				global.LOG.Error("查询客户端信息失败", zap.Error(err))
				return
			}

			// 只有非pipe监听器才发送resize命令
			if client.ListenerType != "pipe" {
				// 发送resize命令到指定终端
				resizeCmd := fmt.Sprintf("RESIZE:%d:%d", resizeMessage.Cols, resizeMessage.Rows)
				err := commandService.SendCommandToTerminal(clientID, resizeCmd, resizeMessage.TerminalID)
				if err != nil {
					global.LOG.Error("发送Resize命令失败", zap.Error(err))
				}
			} else {
				global.LOG.Debug("跳过pipe监听器的resize命令", zap.Uint("clientID", clientID))
			}
			return
		}
		// 尝试解析为多终端命令格式
		type TerminalCommandMessage struct {
			TerminalID uint32 `json:"terminal_id,omitempty"`
			Command    string `json:"command,omitempty"`
			Action     string `json:"action,omitempty"`
		}

		var terminalCmd TerminalCommandMessage
		if err := json.Unmarshal(message, &terminalCmd); err == nil {
			// 处理不同的动作
			switch terminalCmd.Action {

			case "resize":
				// 终端大小调整 - 这种情况不应该出现，因为resize消息已经在上面单独处理了
				global.LOG.Warn("收到意外的resize命令格式", zap.Uint("clientID", clientID))

			default:
				// 普通命令
				if terminalCmd.Command != "" {
					terminalID := terminalCmd.TerminalID // 默认为0（主终端）
					if err := commandService.SendCommandToTerminal(clientID, terminalCmd.Command, terminalID); err != nil {
						global.LOG.Error(fmt.Sprintf("转发多终端命令失败: %s", err.Error()))
					}
				}
			}
		} else {
			// 旧格式：直接发送到主终端（向后兼容）
			if err := commandService.SendCommand(clientID, string(message)); err != nil {
				global.LOG.Error(fmt.Sprintf("转发输入到客户端失败: %s", err.Error()))
			}
		}
	}
}
