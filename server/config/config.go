package config

type Config struct {
	Server   Server     `mapstructure:"server" json:"server" yaml:"server"`
	JWT      JWT        `mapstructure:"jwt" json:"jwt" yaml:"jwt"`
	Zap      Zap        `mapstructure:"zap" json:"zap" yaml:"zap"`
	Captcha  Captcha    `mapstructure:"captcha" json:"captcha" yaml:"captcha"`
	Sqlite   Sqlite     `mapstructure:"sqlite" json:"sqlite" yaml:"sqlite"`
	DiskList []DiskList `mapstructure:"disk-list" json:"disk-list" yaml:"disk-list"`
	Admin    Admin      `mapstructure:"admin" json:"admin" yaml:"admin"`
	// 注意：Scan配置已移除，扫描功能由FScan系统处理
}
